# Bidding System Refactoring Guide

## Quick Start Commands

### 1. Automated Migration (Recommended)
```bash
# Run the migration script
php bidding-migration-script.php /path/to/source/project /path/to/target/project

# Install dependencies
composer install

# Run migrations
php artisan migrate

# Seed settings
php artisan db:seed --class=SettingsSeeder

# Clear caches
php artisan config:clear && php artisan cache:clear && php artisan route:clear
```

### 2. Manual Migration Steps
```bash
# 1. Copy files manually (see file list below)
# 2. Update composer.json
# 3. Install dependencies
composer install && composer dump-autoload

# 4. Run database migrations
php artisan migrate

# 5. Test the system
php artisan route:list | grep bid
```

## Critical Files to Copy First

### Priority 1 (Core Functionality)
```
database/migrations/2023_05_15_095252_update_requests_for_offerred_ride_fare_update_users_for_bidd_ride_table.php
app/Http/Controllers/Api/V1/Request/CreateNewRequestController.php
app/Base/Constants/Setting/Settings.php
config/firebase.php
```

### Priority 2 (Business Logic)
```
app/Helpers/Rides/RidePriceCalculationHelpers.php
app/Transformers/Requests/TripRequestTransformer.php
app/Jobs/NoDriverFoundNotifyJob.php
```

### Priority 3 (Supporting Features)
```
app/Helpers/Rides/CalculatAdminCommissionAndTaxHelper.php
app/Transformers/Driver/DriverProfileTransformer.php
public/push-configurations/bidding_firebase.json
```

## Key Refactoring Points

### 1. Namespace Updates
When copying files, update these namespaces to match your project:

```php
// From
namespace App\Http\Controllers\Api\V1\Request;

// To (if different)
namespace YourApp\Http\Controllers\Api\V1\Request;
```

### 2. Model Updates

#### User Model
```php
// Add to $fillable array
protected $fillable = [
    // ... existing fields
    'is_bid_app',
];
```

#### Request Model
```php
// Add to $fillable array
protected $fillable = [
    // ... existing fields
    'offerred_ride_fare',
    'accepted_ride_fare', 
    'is_bid_ride',
];
```

### 3. Route Registration
```php
// In routes/api/v1/request.php
Route::middleware('auth')->group(function () {
    // ... existing routes
    Route::post('respond-for-bid', 'CreateNewRequestController@respondForBid');
});
```

### 4. Settings Configuration
```php
// Add to Settings constants
const BIDDING_LOW_PERCENTAGE = 'bidding_low_percentage';
const BIDDING_HIGH_PERCENTAGE = 'bidding_high_percentage';
const MAXIMUM_TIME_FOR_FIND_DRIVERS_FOR_BIDDING_RIDE = 'maximum_time_for_find_drivers_for_bitting_ride';
const BIDDING_AMOUNT_INCREASE_OR_DECREASE = 'bidding_amount_increase_or_decrease';
```

## Environment Configuration

### .env Updates
```env
# Firebase Configuration
FIREBASE_CREDENTIALS=/path/to/your/firebase-credentials.json
FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com

# Bidding Configuration  
APP_FOR=bidding
```

### composer.json Updates
```json
{
    "require": {
        "saikiran/geohash": "^1.0",
        "kreait/firebase-php": "^6.0",
        "spatie/laravel-fractal": "^6.0",
        "ramsey/uuid": "^4.5"
    },
    "autoload": {
        "files": [
            "app/Helpers/helpers.php"
        ]
    }
}
```

## Database Schema Changes

### SQL Commands (if running manually)
```sql
-- Add bidding columns to requests table
ALTER TABLE requests ADD COLUMN offerred_ride_fare DOUBLE(15,2) DEFAULT 0 AFTER request_eta_amount;
ALTER TABLE requests ADD COLUMN accepted_ride_fare DOUBLE(15,2) DEFAULT 0 AFTER offerred_ride_fare;
ALTER TABLE requests ADD COLUMN is_bid_ride BOOLEAN DEFAULT FALSE AFTER offerred_ride_fare;

-- Add bidding flag to users table
ALTER TABLE users ADD COLUMN is_bid_app BOOLEAN DEFAULT FALSE AFTER social_provider;
```

## Testing Commands

### API Testing with curl
```bash
# Test bid request creation
curl -X POST http://your-app.com/api/v1/request/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pick_lat": 11.0797,
    "pick_lng": 76.9997,
    "pick_address": "Test Address",
    "vehicle_type": "vehicle_type_id",
    "is_bid_ride": 1,
    "offerred_ride_fare": 100.00
  }'

# Test bid response
curl -X POST http://your-app.com/api/v1/request/respond-for-bid \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "request_id": "request_uuid",
    "driver_id": "driver_id",
    "accepted_ride_fare": 95.00,
    "offerred_ride_fare": 100.00
  }'
```

### Database Testing
```sql
-- Check if bidding columns exist
DESCRIBE requests;
DESCRIBE users;

-- Test bidding data
SELECT id, is_bid_ride, offerred_ride_fare, accepted_ride_fare FROM requests WHERE is_bid_ride = 1;

-- Check settings
SELECT * FROM settings WHERE name LIKE '%bidding%';
```

## Common Issues & Solutions

### 1. Firebase Connection Issues
```php
// Test Firebase connection
use Kreait\Firebase\Contract\Database;

public function testFirebase(Database $database)
{
    try {
        $reference = $database->getReference('test');
        $reference->set(['timestamp' => time()]);
        return 'Firebase connected successfully';
    } catch (Exception $e) {
        return 'Firebase error: ' . $e->getMessage();
    }
}
```

### 2. Missing Dependencies
```bash
# If you get class not found errors
composer install
composer dump-autoload

# Check if packages are installed
composer show | grep -E "(geohash|firebase|fractal)"
```

### 3. Route Not Found
```bash
# Check if routes are registered
php artisan route:list | grep bid

# Clear route cache
php artisan route:clear
```

### 4. Database Migration Issues
```bash
# Check migration status
php artisan migrate:status

# Rollback if needed
php artisan migrate:rollback

# Re-run migrations
php artisan migrate
```

## Validation Checklist

### ✅ Quick Validation Steps
1. **Dependencies**: `composer show | grep -E "(geohash|firebase|fractal)"`
2. **Database**: Check if bidding columns exist
3. **Routes**: `php artisan route:list | grep bid`
4. **Firebase**: Test connection with simple read/write
5. **API**: Test bid creation and response endpoints

### ✅ Functional Testing
1. Create a bid request via API
2. Verify data is stored in database
3. Test bid response functionality
4. Check Firebase real-time updates
5. Verify transformers return correct data

## Performance Considerations

### 1. Database Indexing
```sql
-- Add indexes for bidding queries
CREATE INDEX idx_requests_bid_ride ON requests(is_bid_ride);
CREATE INDEX idx_users_bid_app ON users(is_bid_app);
```

### 2. Firebase Optimization
- Use Firebase rules to limit data access
- Implement proper data pagination
- Cache frequently accessed data

### 3. API Optimization
- Implement request rate limiting
- Use database query optimization
- Add response caching where appropriate

## Security Considerations

### 1. Firebase Security Rules
```javascript
{
  "rules": {
    "requests": {
      "$requestId": {
        ".read": "auth != null",
        ".write": "auth != null && auth.uid == data.child('user_id').val()"
      }
    }
  }
}
```

### 2. API Security
- Validate all input parameters
- Implement proper authentication
- Use HTTPS for all API calls
- Sanitize user inputs

## Deployment Notes

### Production Checklist
- [ ] Firebase credentials are secure
- [ ] Environment variables are set
- [ ] Database migrations are run
- [ ] Settings are seeded
- [ ] Caches are cleared
- [ ] Queue workers are running (if using queues)

### Monitoring
- Monitor Firebase usage and costs
- Track API response times
- Monitor database performance
- Set up error logging and alerts

This refactoring guide provides a comprehensive approach to successfully migrating and implementing the bidding system in your new project.
