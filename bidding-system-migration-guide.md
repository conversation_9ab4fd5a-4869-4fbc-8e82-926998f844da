# Bidding System Migration Guide

## Phase 1: Prerequisites & Dependencies

### 1.1 Install Required Packages

Add these to your target project's `composer.json`:

```json
{
    "require": {
        "saikiran/geohash": "^1.0",
        "kreait/firebase-php": "^6.0",
        "spatie/laravel-fractal": "^6.0",
        "ramsey/uuid": "^4.5"
    }
}
```

Run: `composer install`

### 1.2 Environment Configuration

Add to your `.env` file:

```env
# Firebase Configuration
FIREBASE_CREDENTIALS=/path/to/your/firebase-credentials.json
FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com

# Bidding Configuration
APP_FOR=bidding
```

### 1.3 Firebase Configuration

Create `config/firebase.php` (copy from source project)

## Phase 2: Database Migration

### 2.1 Core Migration File

Copy and run this migration:
`database/migrations/2023_05_15_095252_update_requests_for_offerred_ride_fare_update_users_for_bidd_ride_table.php`

### 2.2 Required Tables

Ensure these tables exist in your target project:
- `requests` (main request table)
- `users` (user table)
- `drivers` (driver table)
- `settings` (application settings)
- `requests_meta` (request metadata)
- `request_cycles` (request lifecycle tracking)

## Phase 3: Core Models Migration

### 3.1 Update Request Model

File: `app/Models/Request/Request.php`

Add these fields to `$fillable`:
```php
'offerred_ride_fare', 'accepted_ride_fare', 'is_bid_ride'
```

### 3.2 Update User Model

File: `app/Models/User.php`

Add to `$fillable`:
```php
'is_bid_app'
```

### 3.3 Copy Supporting Models

Copy these models if they don't exist:
- `app/Models/Request/RequestMeta.php`
- `app/Models/Request/RequestCycles.php`
- `app/Models/Request/RequestBill.php`

## Phase 4: Constants & Settings

### 4.1 Settings Constants

Copy `app/Base/Constants/Setting/Settings.php` and add these constants:

```php
const BIDDING_LOW_PERCENTAGE = 'bidding_low_percentage';
const BIDDING_HIGH_PERCENTAGE = 'bidding_high_percentage';
const MAXIMUM_TIME_FOR_FIND_DRIVERS_FOR_BIDDING_RIDE = 'maximum_time_for_find_drivers_for_bitting_ride';
const BIDDING_AMOUNT_INCREASE_OR_DECREASE = 'bidding_amount_increase_or_decrease';
```

### 4.2 Settings Seeder

Update your settings seeder to include bidding settings:

```php
// In SettingsSeeder.php
if ($this->app_for == 'bidding') {
    $this->settings_from_seeder = array_merge($this->settings_from_seeder, [
        'bidding_high_percentage' => [
            'category' => 'TRIP_SETTINGS',
            'value' => 10,
            'field' => 'TEXT',
        ],
        'bidding_low_percentage' => [
            'category' => 'TRIP_SETTINGS', 
            'value' => 50,
            'field' => 'TEXT',
        ],
    ]);
}
```

## Phase 5: Helper Classes & Traits

### 5.1 Copy Helper Files

Copy these helper files:
- `app/Helpers/Rides/RidePriceCalculationHelpers.php`
- `app/Helpers/Rides/CalculatAdminCommissionAndTaxHelper.php`
- `app/Helpers/Rides/FetchDriversFromFirebaseHelpers.php`

### 5.2 Update Helpers Autoload

Add to `composer.json`:

```json
"autoload": {
    "files": [
        "app/Helpers/helpers.php"
    ]
}
```

## Phase 6: Controllers Migration

### 6.1 Core Bidding Controllers

Copy these controller files:
- `app/Http/Controllers/Api/V1/Request/CreateNewRequestController.php`
- `app/Http/Controllers/Api/V1/Request/CreateRequestController.php` (if needed)
- `app/Http/Controllers/Web/DetailEmailController.php` (for web booking)

### 6.2 Update Base Controller

Ensure your controllers extend the appropriate base controller and have access to:
- Firebase Database injection
- Request model
- Driver model
- User authentication

## Phase 7: Routes Configuration

### 7.1 API Routes

Add to `routes/api/v1/request.php`:

```php
// User routes (authenticated)
Route::middleware('auth')->group(function () {
    // Accept/Decline Bid Request
    Route::post('respond-for-bid', 'CreateNewRequestController@respondForBid');
});
```

### 7.2 Route Dependencies

Ensure these route files exist and are properly included:
- `routes/api/v1/request.php`
- `routes/api/v1/driver.php`
- `routes/api/v1/user.php`

## Phase 8: Transformers

### 8.1 Copy Transformer Files

Copy these transformer files:
- `app/Transformers/Requests/TripRequestTransformer.php`
- `app/Transformers/Driver/DriverProfileTransformer.php`

### 8.2 Update Transformers

Ensure transformers include bidding fields:

```php
// In TripRequestTransformer.php
'offerred_ride_fare' => $request->offerred_ride_fare,
'accepted_ride_fare' => $request->accepted_ride_fare,
'is_bid_ride' => $request->is_bid_ride,
'bidding_low_percentage' => get_settings('bidding_low_percentage'),
'bidding_high_percentage' => get_settings('bidding_high_percentage'),
```

## Phase 9: Jobs & Notifications

### 9.1 Copy Job Files

Copy these job files if they don't exist:
- `app/Jobs/NoDriverFoundNotifyJob.php`
- `app/Jobs/SendRequestToNextDriversJob.php`
- `app/Jobs/Notifications/SendPushNotification.php`

### 9.2 Firebase Integration

Ensure Firebase is properly configured for real-time updates and notifications.

## Phase 10: Request Validation

### 10.1 Update Request Validation

Update `app/Http/Requests/Request/CreateTripRequest.php`:

```php
public function rules()
{
    return [
        // ... existing rules
        'is_bid_ride' => 'sometimes|boolean',
        'offerred_ride_fare' => 'required_if:is_bid_ride,1|numeric|min:0',
    ];
}
```

## Phase 11: Frontend Assets (Optional)

### 11.1 JavaScript Files

Copy if needed:
- `public/assets/js/book-ride.js`

### 11.2 Language Files

Copy language files for internationalization:
- `resources/lang/*/view_pages.php` (add bidding translations)

## Phase 12: Firebase Configuration Files

### 12.1 Firebase Credentials

Copy your Firebase configuration:
- `public/push-configurations/bidding_firebase.json`

Update the file with your project's Firebase credentials.

## Phase 13: Testing & Validation

### 13.1 Test Endpoints

Test these key endpoints:
1. `POST /api/v1/request/create` (with `is_bid_ride=1`)
2. `POST /api/v1/request/respond-for-bid`

### 13.2 Database Verification

Verify these database operations:
1. Creating bid requests
2. Storing offered fare
3. Accepting bids
4. Updating request status

### 13.3 Firebase Integration

Test Firebase real-time updates for:
1. New bid requests
2. Bid acceptance/rejection
3. Driver notifications

## Phase 14: Configuration Adjustments

### 14.1 App Configuration

Update `config/app.php` or create environment-specific configs:

```php
'app_for' => env('APP_FOR', 'bidding'),
```

### 14.2 Service Providers

Register any custom service providers needed for bidding functionality.

## Phase 15: Final Steps

### 15.1 Run Migrations

```bash
php artisan migrate
```

### 15.2 Seed Settings

```bash
php artisan db:seed --class=SettingsSeeder
```

### 15.3 Clear Caches

```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
```

## Troubleshooting

### Common Issues

1. **Firebase Connection**: Ensure credentials file path is correct
2. **Missing Dependencies**: Run `composer install` after updating composer.json
3. **Database Errors**: Verify all required tables exist
4. **Route Conflicts**: Check for duplicate route definitions
5. **Namespace Issues**: Ensure all copied files have correct namespaces

### Debug Steps

1. Check Laravel logs: `storage/logs/laravel.log`
2. Verify Firebase connection: Test with simple Firebase read/write
3. Test API endpoints with Postman/curl
4. Check database for proper data insertion

This migration guide provides a comprehensive approach to extracting and implementing the bidding system in your new project. Follow each phase carefully and test thoroughly at each step.
