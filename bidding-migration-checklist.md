# Bidding System Migration Checklist

## Pre-Migration Setup

### ✅ Prerequisites
- [ ] Target Laravel project is set up and working
- [ ] Source project is accessible
- [ ] Database backup is created
- [ ] Git repository is clean (commit current changes)

### ✅ Environment Preparation
- [ ] PHP 8.0+ is installed
- [ ] Composer is available
- [ ] Database connection is working
- [ ] Firebase account is set up (if using Firebase)

## Phase 1: Dependencies & Configuration

### ✅ Composer Dependencies
- [ ] Add `saikiran/geohash: ^1.0` to composer.json
- [ ] Add `kreait/firebase-php: ^6.0` to composer.json
- [ ] Add `spatie/laravel-fractal: ^6.0` to composer.json
- [ ] Add `ramsey/uuid: ^4.5` to composer.json
- [ ] Run `composer install`

### ✅ Environment Configuration
- [ ] Add `FIREBASE_CREDENTIALS` to .env
- [ ] Add `FIREBASE_DATABASE_URL` to .env
- [ ] Add `APP_FOR=bidding` to .env
- [ ] Copy Firebase credentials JSON file

### ✅ Configuration Files
- [ ] Copy `config/firebase.php`
- [ ] Update Firebase credentials path in config

## Phase 2: Database Migration

### ✅ Migration Files
- [ ] Copy bidding migration file:
  `database/migrations/2023_05_15_095252_update_requests_for_offerred_ride_fare_update_users_for_bidd_ride_table.php`
- [ ] Run `php artisan migrate`
- [ ] Verify new columns exist in database:
  - [ ] `requests.offerred_ride_fare`
  - [ ] `requests.accepted_ride_fare`
  - [ ] `requests.is_bid_ride`
  - [ ] `users.is_bid_app`

### ✅ Required Tables Check
- [ ] `requests` table exists
- [ ] `users` table exists
- [ ] `drivers` table exists
- [ ] `settings` table exists
- [ ] `requests_meta` table exists
- [ ] `request_cycles` table exists

## Phase 3: Core Models

### ✅ User Model Updates
- [ ] Open `app/Models/User.php`
- [ ] Add `'is_bid_app'` to `$fillable` array
- [ ] Test model can save bidding flag

### ✅ Request Model Updates
- [ ] Open `app/Models/Request/Request.php` (or equivalent)
- [ ] Add to `$fillable` array:
  - [ ] `'offerred_ride_fare'`
  - [ ] `'accepted_ride_fare'`
  - [ ] `'is_bid_ride'`
- [ ] Test model can save bidding data

### ✅ Supporting Models
- [ ] Copy `app/Models/Request/RequestMeta.php` (if not exists)
- [ ] Copy `app/Models/Request/RequestCycles.php` (if not exists)
- [ ] Copy `app/Models/Request/RequestBill.php` (if not exists)

## Phase 4: Constants & Settings

### ✅ Settings Constants
- [ ] Copy or update `app/Base/Constants/Setting/Settings.php`
- [ ] Add bidding constants:
  - [ ] `BIDDING_LOW_PERCENTAGE`
  - [ ] `BIDDING_HIGH_PERCENTAGE`
  - [ ] `MAXIMUM_TIME_FOR_FIND_DRIVERS_FOR_BIDDING_RIDE`
  - [ ] `BIDDING_AMOUNT_INCREASE_OR_DECREASE`

### ✅ Settings Seeder
- [ ] Update `database/seeders/SettingsSeeder.php`
- [ ] Add bidding settings with default values
- [ ] Run `php artisan db:seed --class=SettingsSeeder`
- [ ] Verify settings exist in database

## Phase 5: Helper Classes

### ✅ Helper Files
- [ ] Copy `app/Helpers/Rides/RidePriceCalculationHelpers.php`
- [ ] Copy `app/Helpers/Rides/CalculatAdminCommissionAndTaxHelper.php`
- [ ] Copy `app/Helpers/Rides/FetchDriversFromFirebaseHelpers.php`
- [ ] Update `app/Helpers/helpers.php` (add bidding helper functions)

### ✅ Autoload Configuration
- [ ] Add `app/Helpers/helpers.php` to composer.json autoload files
- [ ] Run `composer dump-autoload`

## Phase 6: Controllers

### ✅ Core Controllers
- [ ] Copy `app/Http/Controllers/Api/V1/Request/CreateNewRequestController.php`
- [ ] Copy `app/Http/Controllers/Api/V1/Request/CreateRequestController.php`
- [ ] Copy `app/Http/Controllers/Web/DetailEmailController.php` (if needed)

### ✅ Controller Dependencies
- [ ] Verify all imported classes exist
- [ ] Update namespaces if different
- [ ] Check Firebase Database injection works
- [ ] Test controller instantiation

## Phase 7: Routes

### ✅ API Routes
- [ ] Open `routes/api/v1/request.php`
- [ ] Add bidding route: `Route::post('respond-for-bid', 'CreateNewRequestController@respondForBid')`
- [ ] Verify route is registered: `php artisan route:list | grep bid`
- [ ] Test route accessibility

### ✅ Route Dependencies
- [ ] Ensure controller exists and is accessible
- [ ] Check middleware configuration
- [ ] Verify authentication requirements

## Phase 8: Transformers

### ✅ Transformer Files
- [ ] Copy `app/Transformers/Requests/TripRequestTransformer.php`
- [ ] Copy `app/Transformers/Driver/DriverProfileTransformer.php`

### ✅ Transformer Updates
- [ ] Verify bidding fields are included in transformers:
  - [ ] `offerred_ride_fare`
  - [ ] `accepted_ride_fare`
  - [ ] `is_bid_ride`
  - [ ] `bidding_low_percentage`
  - [ ] `bidding_high_percentage`

## Phase 9: Jobs & Notifications

### ✅ Job Files
- [ ] Copy `app/Jobs/NoDriverFoundNotifyJob.php`
- [ ] Copy `app/Jobs/SendRequestToNextDriversJob.php`
- [ ] Copy notification jobs if needed

### ✅ Queue Configuration
- [ ] Verify queue driver is configured
- [ ] Test job dispatching
- [ ] Check job processing

## Phase 10: Request Validation

### ✅ Validation Rules
- [ ] Update `app/Http/Requests/Request/CreateTripRequest.php`
- [ ] Add bidding validation rules:
  - [ ] `'is_bid_ride' => 'sometimes|boolean'`
  - [ ] `'offerred_ride_fare' => 'required_if:is_bid_ride,1|numeric|min:0'`

## Phase 11: Firebase Integration

### ✅ Firebase Setup
- [ ] Firebase project is created
- [ ] Firebase credentials are valid
- [ ] Firebase Realtime Database is enabled
- [ ] Test Firebase connection

### ✅ Firebase Configuration
- [ ] Copy `public/push-configurations/bidding_firebase.json`
- [ ] Update with your project credentials
- [ ] Test Firebase read/write operations

## Phase 12: Testing

### ✅ Unit Tests
- [ ] Test bid request creation
- [ ] Test bid response functionality
- [ ] Test model relationships
- [ ] Test helper functions

### ✅ API Testing
- [ ] Test `POST /api/v1/request/create` with `is_bid_ride=1`
- [ ] Test `POST /api/v1/request/respond-for-bid`
- [ ] Verify response format matches expected structure
- [ ] Test error scenarios

### ✅ Database Testing
- [ ] Create bid request and verify database entry
- [ ] Accept bid and verify status update
- [ ] Test data integrity

### ✅ Firebase Testing
- [ ] Test real-time updates
- [ ] Test driver notifications
- [ ] Test bid status synchronization

## Phase 13: Final Configuration

### ✅ Cache & Config
- [ ] Run `php artisan config:clear`
- [ ] Run `php artisan cache:clear`
- [ ] Run `php artisan route:clear`
- [ ] Run `php artisan view:clear`

### ✅ Permissions
- [ ] Check file permissions
- [ ] Verify storage directory is writable
- [ ] Check Firebase credentials file permissions

## Phase 14: Production Readiness

### ✅ Security
- [ ] Review Firebase security rules
- [ ] Check API authentication
- [ ] Validate input sanitization
- [ ] Review error handling

### ✅ Performance
- [ ] Test with multiple concurrent requests
- [ ] Monitor database query performance
- [ ] Check Firebase connection limits
- [ ] Optimize if needed

## Phase 15: Documentation

### ✅ Documentation Updates
- [ ] Update API documentation
- [ ] Document bidding flow
- [ ] Create deployment guide
- [ ] Update README

### ✅ Team Communication
- [ ] Inform team about new features
- [ ] Provide testing guidelines
- [ ] Share configuration requirements

## Troubleshooting Checklist

### ✅ Common Issues
- [ ] Firebase connection errors - check credentials
- [ ] Missing dependencies - run composer install
- [ ] Database errors - verify migrations
- [ ] Route not found - check route registration
- [ ] Class not found - check namespaces and autoload

### ✅ Debug Steps
- [ ] Check Laravel logs: `tail -f storage/logs/laravel.log`
- [ ] Test Firebase: Create simple read/write test
- [ ] Verify database: Check tables and columns exist
- [ ] Test API: Use Postman or curl for endpoint testing

## Post-Migration Verification

### ✅ Functionality Tests
- [ ] User can create bid request
- [ ] Driver receives bid notification
- [ ] User can accept/reject driver bid
- [ ] Request status updates correctly
- [ ] Billing calculates correctly

### ✅ Integration Tests
- [ ] End-to-end bidding flow works
- [ ] Real-time updates function
- [ ] Notifications are sent
- [ ] Data persists correctly

## Success Criteria

- [ ] All tests pass
- [ ] Bidding flow works end-to-end
- [ ] No critical errors in logs
- [ ] Performance is acceptable
- [ ] Documentation is complete

## Rollback Plan

If migration fails:
- [ ] Restore database from backup
- [ ] Restore code from Git
- [ ] Remove added dependencies
- [ ] Revert configuration changes

---

**Migration Status**: ⏳ In Progress / ✅ Complete / ❌ Failed

**Notes**: 
_Add any specific notes or issues encountered during migration_
