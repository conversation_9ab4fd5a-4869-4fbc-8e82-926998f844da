# Bidding System Files Export List

## Core Files to Copy (Required)

### 1. Database Migrations
```
database/migrations/2023_05_15_095252_update_requests_for_offerred_ride_fare_update_users_for_bidd_ride_table.php
```

### 2. Models
```
app/Models/Request/Request.php (update existing)
app/Models/User.php (update existing)
app/Models/Request/RequestMeta.php
app/Models/Request/RequestCycles.php
app/Models/Request/RequestBill.php
```

### 3. Controllers
```
app/Http/Controllers/Api/V1/Request/CreateNewRequestController.php
app/Http/Controllers/Api/V1/Request/CreateRequestController.php
app/Http/Controllers/Web/DetailEmailController.php
```

### 4. Constants & Settings
```
app/Base/Constants/Setting/Settings.php (update existing)
database/seeders/SettingsSeeder.php (update existing)
```

### 5. Helper Classes
```
app/Helpers/Rides/RidePriceCalculationHelpers.php
app/Helpers/Rides/CalculatAdminCommissionAndTaxHelper.php
app/Helpers/Rides/FetchDriversFromFirebaseHelpers.php
app/Helpers/helpers.php (update existing)
```

### 6. Transformers
```
app/Transformers/Requests/TripRequestTransformer.php
app/Transformers/Driver/DriverProfileTransformer.php
```

### 7. Routes
```
routes/api/v1/request.php (update existing)
```

### 8. Jobs & Notifications
```
app/Jobs/NoDriverFoundNotifyJob.php
app/Jobs/SendRequestToNextDriversJob.php
app/Jobs/Notifications/SendPushNotification.php
```

### 9. Request Validation
```
app/Http/Requests/Request/CreateTripRequest.php (update existing)
```

### 10. Configuration Files
```
config/firebase.php
public/push-configurations/bidding_firebase.json
```

## Optional Files (For Complete Feature Set)

### 11. Frontend Assets
```
public/assets/js/book-ride.js
resources/lang/*/view_pages.php (update for bidding translations)
```

### 12. Admin Views (if needed)
```
resources/views/admin/delivery_request/_request.blade.php
resources/views/admin/vehicle_fare/_set_price.blade.php
```

## Dependencies to Add

### Composer Packages
```json
{
    "require": {
        "saikiran/geohash": "^1.0",
        "kreait/firebase-php": "^6.0",
        "spatie/laravel-fractal": "^6.0",
        "ramsey/uuid": "^4.5"
    }
}
```

### Environment Variables
```env
FIREBASE_CREDENTIALS=/path/to/firebase-credentials.json
FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com
APP_FOR=bidding
```

## File Modifications Required

### 1. Update Request Model
Add to `$fillable` array:
```php
'offerred_ride_fare', 'accepted_ride_fare', 'is_bid_ride'
```

### 2. Update User Model
Add to `$fillable` array:
```php
'is_bid_app'
```

### 3. Update Settings Constants
Add bidding-related constants:
```php
const BIDDING_LOW_PERCENTAGE = 'bidding_low_percentage';
const BIDDING_HIGH_PERCENTAGE = 'bidding_high_percentage';
const MAXIMUM_TIME_FOR_FIND_DRIVERS_FOR_BIDDING_RIDE = 'maximum_time_for_find_drivers_for_bitting_ride';
const BIDDING_AMOUNT_INCREASE_OR_DECREASE = 'bidding_amount_increase_or_decrease';
```

### 4. Update Routes
Add bidding routes to API routes file:
```php
Route::post('respond-for-bid', 'CreateNewRequestController@respondForBid');
```

### 5. Update Composer Autoload
Add helpers to autoload:
```json
"autoload": {
    "files": [
        "app/Helpers/helpers.php"
    ]
}
```

## Namespace Adjustments

When copying files, ensure these namespaces match your target project:

### Controllers
```php
namespace App\Http\Controllers\Api\V1\Request;
namespace App\Http\Controllers\Web;
```

### Models
```php
namespace App\Models\Request;
namespace App\Models;
```

### Helpers
```php
namespace App\Helpers\Rides;
```

### Jobs
```php
namespace App\Jobs;
namespace App\Jobs\Notifications;
```

### Transformers
```php
namespace App\Transformers\Requests;
namespace App\Transformers\Driver;
```

## Database Schema Changes

### Requests Table
```sql
ALTER TABLE requests ADD COLUMN offerred_ride_fare DOUBLE(15,2) DEFAULT 0;
ALTER TABLE requests ADD COLUMN accepted_ride_fare DOUBLE(15,2) DEFAULT 0;
ALTER TABLE requests ADD COLUMN is_bid_ride BOOLEAN DEFAULT FALSE;
```

### Users Table
```sql
ALTER TABLE users ADD COLUMN is_bid_app BOOLEAN DEFAULT FALSE;
```

## Configuration Updates

### Firebase Config
Create `config/firebase.php` with proper Firebase configuration.

### App Config
Update app configuration to support bidding mode:
```php
'app_for' => env('APP_FOR', 'bidding'),
```

## Testing Files (Optional)

If you want to include tests:
```
tests/Feature/BiddingSystemTest.php
tests/Unit/BiddingHelpersTest.php
```

## Documentation Files

```
README-bidding.md
API-documentation-bidding.md
```

## Priority Order for Migration

1. **High Priority (Core Functionality)**
   - Database migrations
   - Models updates
   - Core controllers
   - Settings constants
   - Routes

2. **Medium Priority (Business Logic)**
   - Helper classes
   - Transformers
   - Jobs & notifications
   - Request validation

3. **Low Priority (Enhancement)**
   - Frontend assets
   - Admin views
   - Language files
   - Documentation

## Verification Checklist

After copying files, verify:
- [ ] All namespaces are correct
- [ ] All dependencies are installed
- [ ] Database migrations run successfully
- [ ] Firebase connection works
- [ ] API endpoints respond correctly
- [ ] Bidding flow works end-to-end
- [ ] Settings are properly seeded
- [ ] Real-time notifications work

This export list provides a comprehensive overview of all files needed to successfully migrate the bidding system to your new project.
