[{"capital": "Kabul", "citizenship": "Afghan", "country_code": "004", "currency": "a<PERSON>ghani", "currency_code": "AFN", "currency_sub_unit": "pul", "full_name": "Islamic Republic of Afghanistan", "iso_3166_2": "AF", "iso_3166_3": "AFG", "name": "Afghanistan", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+93", "currency_symbol": "؋", "currency_decimals": "2", "flag": "AF.png", "minLength": 7, "maxLength": 14}, {"capital": "Tirana", "citizenship": "Albanian", "country_code": "008", "currency": "lek", "currency_code": "ALL", "currency_sub_unit": "(qindar (pl. qindarka))", "full_name": "Republic of Albania", "iso_3166_2": "AL", "iso_3166_3": "ALB", "name": "Albania", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+355", "currency_symbol": "Lek", "currency_decimals": "2", "flag": "AL.png", "minLength": 7, "maxLength": 14}, {"capital": "Antartica", "citizenship": "of Antartica", "country_code": "010", "currency": "", "currency_code": "", "currency_sub_unit": "", "full_name": "Antarctica", "iso_3166_2": "AQ", "iso_3166_3": "ATA", "name": "Antarctica", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+672", "currency_symbol": "", "currency_decimals": "2", "flag": "AQ.png", "minLength": 7, "maxLength": 14}, {"capital": "Algiers", "citizenship": "Algerian", "country_code": "012", "currency": "Algerian dinar", "currency_code": "DZD", "currency_sub_unit": "centime", "full_name": "People’s Democratic Republic of Algeria", "iso_3166_2": "DZ", "iso_3166_3": "DZA", "name": "Algeria", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+213", "currency_symbol": "DZD", "currency_decimals": "2", "flag": "DZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Pago Pago", "citizenship": "American Samoan", "country_code": "016", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Territory of American", "iso_3166_2": "AS", "iso_3166_3": "ASM", "name": "American Samoa", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "AS.png", "minLength": 7, "maxLength": 14}, {"capital": "Andorra la Vella", "citizenship": "Andorran", "country_code": "020", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Principality of Andorra", "iso_3166_2": "AD", "iso_3166_3": "AND", "name": "Andorra", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+376", "currency_symbol": "€", "currency_decimals": "2", "flag": "AD.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Angolan", "country_code": "024", "currency": "kwanza", "currency_code": "AOA", "currency_sub_unit": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "Republic of Angola", "iso_3166_2": "AO", "iso_3166_3": "AGO", "name": "Angola", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+244", "currency_symbol": "Kz", "currency_decimals": "2", "flag": "AO.png", "minLength": 7, "maxLength": 14}, {"capital": "St John’s", "citizenship": "of Antigua and Barbuda", "country_code": "028", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "Antigua and Barbuda", "iso_3166_2": "AG", "iso_3166_3": "ATG", "name": "Antigua and Barbuda", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "AG.png", "minLength": 7, "maxLength": 14}, {"capital": "Baku", "citizenship": "Azerbaijani", "country_code": "031", "currency": "Azerbaijani manat", "currency_code": "AZN", "currency_sub_unit": "kepik (inv.)", "full_name": "Republic of Azerbaijan", "iso_3166_2": "AZ", "iso_3166_3": "AZE", "name": "Azerbaijan", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+994", "currency_symbol": "ман", "currency_decimals": "2", "flag": "AZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Buenos Aires", "citizenship": "Argentinian", "country_code": "032", "currency": "Argentine peso", "currency_code": "ARS", "currency_sub_unit": "centavo", "full_name": "Argentine Republic", "iso_3166_2": "AR", "iso_3166_3": "ARG", "name": "Argentina", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+54", "currency_symbol": "$", "currency_decimals": "2", "flag": "AR.png", "minLength": 7, "maxLength": 14}, {"capital": "Canberra", "citizenship": "Australian", "country_code": "036", "currency": "Australian dollar", "currency_code": "AUD", "currency_sub_unit": "cent", "full_name": "Commonwealth of Australia", "iso_3166_2": "AU", "iso_3166_3": "AUS", "name": "Australia", "region_code": "009", "sub_region_code": "053", "eea": false, "dial_code": "+61", "currency_symbol": "$", "currency_decimals": "2", "flag": "AU.png", "minLength": 7, "maxLength": 14}, {"capital": "Vienna", "citizenship": "Austrian", "country_code": "040", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Austria", "iso_3166_2": "AT", "iso_3166_3": "AUT", "name": "Austria", "region_code": "150", "sub_region_code": "155", "eea": true, "dial_code": "+43", "currency_symbol": "€", "currency_decimals": "2", "flag": "AT.png", "minLength": 7, "maxLength": 14}, {"capital": "Nassau", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "044", "currency": "Bahamian dollar", "currency_code": "BSD", "currency_sub_unit": "cent", "full_name": "Commonwealth of the Bahamas", "iso_3166_2": "BS", "iso_3166_3": "BHS", "name": "Bahamas", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "BS.png", "minLength": 7, "maxLength": 14}, {"capital": "Manama", "citizenship": "Bahraini", "country_code": "048", "currency": "<PERSON><PERSON> dinar", "currency_code": "BHD", "currency_sub_unit": "fils (inv.)", "full_name": "Kingdom of Bahrain", "iso_3166_2": "BH", "iso_3166_3": "BHR", "name": "Bahrain", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+973", "currency_symbol": "BHD", "currency_decimals": "3", "flag": "BH.png", "minLength": 7, "maxLength": 14}, {"capital": "Dhaka", "citizenship": "Bangladeshi", "country_code": "050", "currency": "taka (inv.)", "currency_code": "BDT", "currency_sub_unit": "poisha (inv.)", "full_name": "People’s Republic of Bangladesh", "iso_3166_2": "BD", "iso_3166_3": "BGD", "name": "Bangladesh", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+880", "currency_symbol": "BDT", "currency_decimals": "2", "flag": "BD.png", "minLength": 7, "maxLength": 14}, {"capital": "Yerevan", "citizenship": "Armenian", "country_code": "051", "currency": "dram (inv.)", "currency_code": "AMD", "currency_sub_unit": "luma", "full_name": "Republic of Armenia", "iso_3166_2": "AM", "iso_3166_3": "ARM", "name": "Armenia", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+374", "currency_symbol": "AMD", "currency_decimals": "2", "flag": "AM.png", "minLength": 7, "maxLength": 14}, {"capital": "Bridgetown", "citizenship": "Barbadian", "country_code": "052", "currency": "Barbados dollar", "currency_code": "BBD", "currency_sub_unit": "cent", "full_name": "Barbados", "iso_3166_2": "BB", "iso_3166_3": "BRB", "name": "Barbados", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "BB.png", "minLength": 7, "maxLength": 14}, {"capital": "Brussels", "citizenship": "Belgian", "country_code": "056", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Kingdom of Belgium", "iso_3166_2": "BE", "iso_3166_3": "BEL", "name": "Belgium", "region_code": "150", "sub_region_code": "155", "eea": true, "dial_code": "+32", "currency_symbol": "€", "currency_decimals": "2", "flag": "BE.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON>", "citizenship": "<PERSON><PERSON><PERSON><PERSON>", "country_code": "060", "currency": "Bermuda dollar", "currency_code": "BMD", "currency_sub_unit": "cent", "full_name": "Bermuda", "iso_3166_2": "BM", "iso_3166_3": "BMU", "name": "Bermuda", "region_code": "019", "sub_region_code": "021", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "BM.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON><PERSON>", "citizenship": "Bhutanese", "country_code": "064", "currency": "ngultrum (inv.)", "currency_code": "BTN", "currency_sub_unit": "chhetrum (inv.)", "full_name": "Kingdom of Bhutan", "iso_3166_2": "BT", "iso_3166_3": "BTN", "name": "Bhutan", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+975", "currency_symbol": "BTN", "currency_decimals": "2", "flag": "BT.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON> (BO1)", "citizenship": "Bolivian", "country_code": "068", "currency": "boliviano", "currency_code": "BOB", "currency_sub_unit": "centavo", "full_name": "Plurinational State of Bolivia", "iso_3166_2": "BO", "iso_3166_3": "BOL", "name": "Bolivia, Plurinational State of", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+591", "currency_symbol": "$b", "currency_decimals": "2", "flag": "BO.png", "minLength": 7, "maxLength": 14}, {"capital": "Sarajevo", "citizenship": "of Bosnia and Herzegovina", "country_code": "070", "currency": "convertible mark", "currency_code": "BAM", "currency_sub_unit": "fening", "full_name": "Bosnia and Herzegovina", "iso_3166_2": "BA", "iso_3166_3": "BIH", "name": "Bosnia and Herzegovina", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+387", "currency_symbol": "KM", "currency_decimals": "2", "flag": "BA.png", "minLength": 7, "maxLength": 14}, {"capital": "Gaborone", "citizenship": "Botswanan", "country_code": "072", "currency": "pula (inv.)", "currency_code": "BWP", "currency_sub_unit": "thebe (inv.)", "full_name": "Republic of Botswana", "iso_3166_2": "BW", "iso_3166_3": "BWA", "name": "Botswana", "region_code": "002", "sub_region_code": "018", "eea": false, "dial_code": "+267", "currency_symbol": "P", "currency_decimals": "2", "flag": "BW.png", "minLength": 7, "maxLength": 14}, {"capital": "Bouvet island", "citizenship": "of Bouvet island", "country_code": "074", "currency": "", "currency_code": "", "currency_sub_unit": "", "full_name": "Bouvet Island", "iso_3166_2": "BV", "iso_3166_3": "BVT", "name": "Bouvet Island", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+47", "currency_symbol": "kr", "currency_decimals": "2", "flag": "BV.png", "minLength": 7, "maxLength": 14}, {"capital": "Brasilia", "citizenship": "Brazilian", "country_code": "076", "currency": "real (pl. reais)", "currency_code": "BRL", "currency_sub_unit": "centavo", "full_name": "Federative Republic of Brazil", "iso_3166_2": "BR", "iso_3166_3": "BRA", "name": "Brazil", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+55", "currency_symbol": "R$", "currency_decimals": "2", "flag": "BR.png", "minLength": 7, "maxLength": 14}, {"capital": "Belmopan", "citizenship": "Belizean", "country_code": "084", "currency": "Belize dollar", "currency_code": "BZD", "currency_sub_unit": "cent", "full_name": "Belize", "iso_3166_2": "BZ", "iso_3166_3": "BLZ", "name": "Belize", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+501", "currency_symbol": "BZ$", "currency_decimals": "2", "flag": "BZ.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON>", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "086", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "British Indian Ocean Territory", "iso_3166_2": "IO", "iso_3166_3": "IOT", "name": "British Indian Ocean Territory", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+246", "currency_symbol": "$", "currency_decimals": "2", "flag": "IO.png", "minLength": 7, "maxLength": 14}, {"capital": "Honiara", "citizenship": "Solomon Islander", "country_code": "090", "currency": "Solomon Islands dollar", "currency_code": "SBD", "currency_sub_unit": "cent", "full_name": "Solomon Islands", "iso_3166_2": "SB", "iso_3166_3": "SLB", "name": "Solomon Islands", "region_code": "009", "sub_region_code": "054", "eea": false, "dial_code": "+677", "currency_symbol": "$", "currency_decimals": "2", "flag": "SB.png", "minLength": 7, "maxLength": 14}, {"capital": "Road Town", "citizenship": "British Virgin Islander;", "country_code": "092", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "British Virgin Islands", "iso_3166_2": "VG", "iso_3166_3": "VGB", "name": "Virgin Islands, British", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "VG.png", "minLength": 7, "maxLength": 14}, {"capital": "Bandar Seri Begawan", "citizenship": "Bruneian", "country_code": "096", "currency": "Brunei dollar", "currency_code": "BND", "currency_sub_unit": "sen (inv.)", "full_name": "Brunei Darussalam", "iso_3166_2": "BN", "iso_3166_3": "BRN", "name": "Brunei Darussalam", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+673", "currency_symbol": "$", "currency_decimals": "2", "flag": "BN.png", "minLength": 7, "maxLength": 14}, {"capital": "Sofia", "citizenship": "Bulgarian", "country_code": "100", "currency": "lev (pl. leva)", "currency_code": "BGN", "currency_sub_unit": "s<PERSON><PERSON>ka", "full_name": "Republic of Bulgaria", "iso_3166_2": "BG", "iso_3166_3": "BGR", "name": "Bulgaria", "region_code": "150", "sub_region_code": "151", "eea": true, "dial_code": "+359", "currency_symbol": "лв", "currency_decimals": "2", "flag": "BG.png", "minLength": 7, "maxLength": 14}, {"capital": "Yangon", "citizenship": "Burmese", "country_code": "104", "currency": "kyat", "currency_code": "MMK", "currency_sub_unit": "pya", "full_name": "Union of Myanmar/", "iso_3166_2": "MM", "iso_3166_3": "MMR", "name": "Myanmar", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+95", "currency_symbol": "K", "currency_decimals": "2", "flag": "MM.png", "minLength": 7, "maxLength": 14}, {"capital": "Bujumbura", "citizenship": "Burundian", "country_code": "108", "currency": "Burundi franc", "currency_code": "BIF", "currency_sub_unit": "centime", "full_name": "Republic of Burundi", "iso_3166_2": "BI", "iso_3166_3": "BDI", "name": "Burundi", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+257", "currency_symbol": "BIF", "currency_decimals": "0", "flag": "BI.png", "minLength": 7, "maxLength": 14}, {"capital": "Minsk", "citizenship": "Belarusian", "country_code": "112", "currency": "Belarusian rouble", "currency_code": "BYR", "currency_sub_unit": "kopek", "full_name": "Republic of Belarus", "iso_3166_2": "BY", "iso_3166_3": "BLR", "name": "Belarus", "region_code": "150", "sub_region_code": "151", "eea": false, "dial_code": "+375", "currency_symbol": "p.", "currency_decimals": "2", "flag": "BY.png", "minLength": 7, "maxLength": 14}, {"capital": "Phnom Penh", "citizenship": "Cambodian", "country_code": "116", "currency": "riel", "currency_code": "KHR", "currency_sub_unit": "sen (inv.)", "full_name": "Kingdom of Cambodia", "iso_3166_2": "KH", "iso_3166_3": "KHM", "name": "Cambodia", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+855", "currency_symbol": "៛", "currency_decimals": "2", "flag": "KH.png", "minLength": 7, "maxLength": 14}, {"capital": "Yaoundé", "citizenship": "Cameroonian", "country_code": "120", "currency": "CFA franc (BEAC)", "currency_code": "XAF", "currency_sub_unit": "centime", "full_name": "Republic of Cameroon", "iso_3166_2": "CM", "iso_3166_3": "CMR", "name": "Cameroon", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+237", "currency_symbol": "FCF", "currency_decimals": "0", "flag": "CM.png", "minLength": 7, "maxLength": 14}, {"capital": "Ottawa", "citizenship": "Canadian", "country_code": "124", "currency": "Canadian dollar", "currency_code": "CAD", "currency_sub_unit": "cent", "full_name": "Canada", "iso_3166_2": "CA", "iso_3166_3": "CAN", "name": "Canada", "region_code": "019", "sub_region_code": "021", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "CA.png", "minLength": 7, "maxLength": 14}, {"capital": "Praia", "citizenship": "Cape Verdean", "country_code": "132", "currency": "Cape Verde escudo", "currency_code": "CVE", "currency_sub_unit": "centavo", "full_name": "Republic of Cape Verde", "iso_3166_2": "CV", "iso_3166_3": "CPV", "name": "Cape Verde", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+238", "currency_symbol": "CVE", "currency_decimals": "2", "flag": "CV.png", "minLength": 7, "maxLength": 14}, {"capital": "George Town", "citizenship": "Caymanian", "country_code": "136", "currency": "Cayman Islands dollar", "currency_code": "KYD", "currency_sub_unit": "cent", "full_name": "Cayman Islands", "iso_3166_2": "KY", "iso_3166_3": "CYM", "name": "Cayman Islands", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "KY.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Central African", "country_code": "140", "currency": "CFA franc (BEAC)", "currency_code": "XAF", "currency_sub_unit": "centime", "full_name": "Central African Republic", "iso_3166_2": "CF", "iso_3166_3": "CAF", "name": "Central African Republic", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+236", "currency_symbol": "CFA", "currency_decimals": "0", "flag": "CF.png", "minLength": 7, "maxLength": 14}, {"capital": "Colombo", "citizenship": "Sri Lankan", "country_code": "144", "currency": "Sri Lankan rupee", "currency_code": "LKR", "currency_sub_unit": "cent", "full_name": "Democratic Socialist Republic of Sri Lanka", "iso_3166_2": "LK", "iso_3166_3": "LKA", "name": "Sri Lanka", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+94", "currency_symbol": "₨", "currency_decimals": "2", "flag": "LK.png", "minLength": 7, "maxLength": 14}, {"capital": "N’Djamena", "citizenship": "Chadian", "country_code": "148", "currency": "CFA franc (BEAC)", "currency_code": "XAF", "currency_sub_unit": "centime", "full_name": "Republic of Chad", "iso_3166_2": "TD", "iso_3166_3": "TCD", "name": "Chad", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+235", "currency_symbol": "XAF", "currency_decimals": "0", "flag": "TD.png", "minLength": 7, "maxLength": 14}, {"capital": "Santiago", "citizenship": "Chilean", "country_code": "152", "currency": "Chilean peso", "currency_code": "CLP", "currency_sub_unit": "centavo", "full_name": "Republic of Chile", "iso_3166_2": "CL", "iso_3166_3": "CHL", "name": "Chile", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+56", "currency_symbol": "CLP", "currency_decimals": "0", "flag": "CL.png", "minLength": 7, "maxLength": 14}, {"capital": "Beijing", "citizenship": "Chinese", "country_code": "156", "currency": "ren<PERSON><PERSON>-yuan (inv.)", "currency_code": "CNY", "currency_sub_unit": "<PERSON><PERSON> (10)", "full_name": "People’s Republic of China", "iso_3166_2": "CN", "iso_3166_3": "CHN", "name": "China", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+86", "currency_symbol": "¥", "currency_decimals": "2", "flag": "CN.png", "minLength": 7, "maxLength": 14}, {"capital": "Taipei", "citizenship": "Taiwanese", "country_code": "158", "currency": "new Taiwan dollar", "currency_code": "TWD", "currency_sub_unit": "fen (inv.)", "full_name": "Republic of China, Taiwan (TW1)", "iso_3166_2": "TW", "iso_3166_3": "TWN", "name": "Taiwan, Province of China", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+886", "currency_symbol": "NT$", "currency_decimals": "2", "flag": "TW.png", "minLength": 7, "maxLength": 14}, {"capital": "Flying Fish Cove", "citizenship": "Christmas Islander", "country_code": "162", "currency": "Australian dollar", "currency_code": "AUD", "currency_sub_unit": "cent", "full_name": "Christmas Island Territory", "iso_3166_2": "CX", "iso_3166_3": "CXR", "name": "Christmas Island", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+61", "currency_symbol": "$", "currency_decimals": "2", "flag": "CX.png", "minLength": 7, "maxLength": 14}, {"capital": "Bantam", "citizenship": "Cocos Islander", "country_code": "166", "currency": "Australian dollar", "currency_code": "AUD", "currency_sub_unit": "cent", "full_name": "Territory of Cocos (Keeling) Islands", "iso_3166_2": "CC", "iso_3166_3": "CCK", "name": "Cocos (Keeling) Islands", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+61", "currency_symbol": "$", "currency_decimals": "2", "flag": "CC.png", "minLength": 7, "maxLength": 14}, {"capital": "Santa Fe de Bogotá", "citizenship": "Colombian", "country_code": "170", "currency": "Colombian peso", "currency_code": "COP", "currency_sub_unit": "centavo", "full_name": "Republic of Colombia", "iso_3166_2": "CO", "iso_3166_3": "COL", "name": "Colombia", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+57", "currency_symbol": "$", "currency_decimals": "2", "flag": "CO.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>", "citizenship": "<PERSON><PERSON>", "country_code": "174", "currency": "Comorian franc", "currency_code": "KMF", "currency_sub_unit": "", "full_name": "Union of the Comoros", "iso_3166_2": "KM", "iso_3166_3": "COM", "name": "Comoros", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+269", "currency_symbol": "KMF", "currency_decimals": "0", "flag": "KM.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "175", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Departmental Collectivity of Mayotte", "iso_3166_2": "YT", "iso_3166_3": "MYT", "name": "Mayotte", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+262", "currency_symbol": "€", "currency_decimals": "2", "flag": "YT.png", "minLength": 7, "maxLength": 14}, {"capital": "Brazzaville", "citizenship": "Congolese", "country_code": "178", "currency": "CFA franc (BEAC)", "currency_code": "XAF", "currency_sub_unit": "centime", "full_name": "Republic of the Congo", "iso_3166_2": "CG", "iso_3166_3": "COG", "name": "Congo", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+242", "currency_symbol": "FCF", "currency_decimals": "0", "flag": "CG.png", "minLength": 7, "maxLength": 14}, {"capital": "Kinshasa", "citizenship": "Congolese", "country_code": "180", "currency": "Congolese franc", "currency_code": "CDF", "currency_sub_unit": "centime", "full_name": "Democratic Republic of the Congo", "iso_3166_2": "CD", "iso_3166_3": "COD", "name": "Congo, the Democratic Republic of the Congo", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+243", "currency_symbol": "CDF", "currency_decimals": "2", "flag": "CD.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>", "citizenship": "Cook Islander", "country_code": "184", "currency": "New Zealand dollar", "currency_code": "NZD", "currency_sub_unit": "cent", "full_name": "Cook Islands", "iso_3166_2": "CK", "iso_3166_3": "COK", "name": "Cook Islands", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+682", "currency_symbol": "$", "currency_decimals": "2", "flag": "CK.png", "minLength": 7, "maxLength": 14}, {"capital": "San José", "citizenship": "Costa Rican", "country_code": "188", "currency": "Costa Rican colón (pl. colones)", "currency_code": "CRC", "currency_sub_unit": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "Republic of Costa Rica", "iso_3166_2": "CR", "iso_3166_3": "CRI", "name": "Costa Rica", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+506", "currency_symbol": "₡", "currency_decimals": "2", "flag": "CR.png", "minLength": 7, "maxLength": 14}, {"capital": "Zagreb", "citizenship": "Croatian", "country_code": "191", "currency": "kuna (inv.)", "currency_code": "HRK", "currency_sub_unit": "lipa (inv.)", "full_name": "Republic of Croatia", "iso_3166_2": "HR", "iso_3166_3": "HRV", "name": "Croatia", "region_code": "150", "sub_region_code": "039", "eea": true, "dial_code": "+385", "currency_symbol": "kn", "currency_decimals": "2", "flag": "HR.png", "minLength": 7, "maxLength": 14}, {"capital": "Havana", "citizenship": "Cuban", "country_code": "192", "currency": "Cuban peso", "currency_code": "CUP", "currency_sub_unit": "centavo", "full_name": "Republic of Cuba", "iso_3166_2": "CU", "iso_3166_3": "CUB", "name": "Cuba", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+53", "currency_symbol": "₱", "currency_decimals": "2", "flag": "CU.png", "minLength": 7, "maxLength": 14}, {"capital": "Nicosia", "citizenship": "Cypriot", "country_code": "196", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Cyprus", "iso_3166_2": "CY", "iso_3166_3": "CYP", "name": "Cyprus", "region_code": "142", "sub_region_code": "145", "eea": true, "dial_code": "+357", "currency_symbol": "CYP", "currency_decimals": "2", "flag": "CY.png", "minLength": 7, "maxLength": 14}, {"capital": "Prague", "citizenship": "Czech", "country_code": "203", "currency": "Czech koruna (pl. koruny)", "currency_code": "CZK", "currency_sub_unit": "<PERSON><PERSON><PERSON>", "full_name": "Czech Republic", "iso_3166_2": "CZ", "iso_3166_3": "CZE", "name": "Czech Republic", "region_code": "150", "sub_region_code": "151", "eea": true, "dial_code": "+420", "currency_symbol": "Kč", "currency_decimals": "2", "flag": "CZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Porto Novo (BJ1)", "citizenship": "Beninese", "country_code": "204", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Republic of Benin", "iso_3166_2": "BJ", "iso_3166_3": "BEN", "name": "Benin", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+229", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "BJ.png", "minLength": 7, "maxLength": 14}, {"capital": "Copenhagen", "citizenship": "Danish", "country_code": "208", "currency": "Danish krone", "currency_code": "DKK", "currency_sub_unit": "øre (inv.)", "full_name": "Kingdom of Denmark", "iso_3166_2": "DK", "iso_3166_3": "DNK", "name": "Denmark", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+45", "currency_symbol": "kr", "currency_decimals": "2", "flag": "DK.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Dominican", "country_code": "212", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "Commonwealth of Dominica", "iso_3166_2": "DM", "iso_3166_3": "DMA", "name": "Dominica", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "DM.png", "minLength": 7, "maxLength": 14}, {"capital": "Santo Domingo", "citizenship": "Dominican", "country_code": "214", "currency": "Dominican peso", "currency_code": "DOP", "currency_sub_unit": "centavo", "full_name": "Dominican Republic", "iso_3166_2": "DO", "iso_3166_3": "DOM", "name": "Dominican Republic", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "RD$", "currency_decimals": "2", "flag": "DO.png", "minLength": 7, "maxLength": 14}, {"capital": "Quito", "citizenship": "Ecuadorian", "country_code": "218", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Republic of Ecuador", "iso_3166_2": "EC", "iso_3166_3": "ECU", "name": "Ecuador", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+593", "currency_symbol": "$", "currency_decimals": "2", "flag": "EC.png", "minLength": 7, "maxLength": 14}, {"capital": "San Salvador", "citizenship": "Salvadoran", "country_code": "222", "currency": "Salvadorian co<PERSON> (pl. colones)", "currency_code": "SVC", "currency_sub_unit": "centavo", "full_name": "Republic of El Salvador", "iso_3166_2": "SV", "iso_3166_3": "SLV", "name": "El Salvador", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+503", "currency_symbol": "$", "currency_decimals": "2", "flag": "SV.png", "minLength": 7, "maxLength": 14}, {"capital": "Malabo", "citizenship": "Equatorial Guinean", "country_code": "226", "currency": "CFA franc (BEAC)", "currency_code": "XAF", "currency_sub_unit": "centime", "full_name": "Republic of Equatorial Guinea", "iso_3166_2": "GQ", "iso_3166_3": "GNQ", "name": "Equatorial Guinea", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+240", "currency_symbol": "FCF", "currency_decimals": "2", "flag": "GQ.png", "minLength": 7, "maxLength": 14}, {"capital": "Addis A<PERSON>ba", "citizenship": "Ethiopian", "country_code": "231", "currency": "birr (inv.)", "currency_code": "ETB", "currency_sub_unit": "cent", "full_name": "Federal Democratic Republic of Ethiopia", "iso_3166_2": "ET", "iso_3166_3": "ETH", "name": "Ethiopia", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+251", "currency_symbol": "ETB", "currency_decimals": "2", "flag": "ET.png", "minLength": 7, "maxLength": 14}, {"capital": "Asmara", "citizenship": "Eritrean", "country_code": "232", "currency": "nakfa", "currency_code": "ERN", "currency_sub_unit": "cent", "full_name": "State of Eritrea", "iso_3166_2": "ER", "iso_3166_3": "ERI", "name": "Eritrea", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+291", "currency_symbol": "Nfk", "currency_decimals": "2", "flag": "ER.png", "minLength": 7, "maxLength": 14}, {"capital": "Tallinn", "citizenship": "Estonian", "country_code": "233", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Estonia", "iso_3166_2": "EE", "iso_3166_3": "EST", "name": "Estonia", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+372", "currency_symbol": "kr", "currency_decimals": "2", "flag": "EE.png", "minLength": 7, "maxLength": 14}, {"capital": "Tórshavn", "citizenship": "Faeroese", "country_code": "234", "currency": "Danish krone", "currency_code": "DKK", "currency_sub_unit": "øre (inv.)", "full_name": "Faeroe Islands", "iso_3166_2": "FO", "iso_3166_3": "FRO", "name": "Faroe Islands", "region_code": "150", "sub_region_code": "154", "eea": false, "dial_code": "+298", "currency_symbol": "kr", "currency_decimals": "2", "flag": "FO.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON>", "citizenship": "Falkland Islander", "country_code": "238", "currency": "Falkland Islands pound", "currency_code": "FKP", "currency_sub_unit": "new penny", "full_name": "Falkland Islands", "iso_3166_2": "FK", "iso_3166_3": "FLK", "name": "Falkland Islands (Malvinas)", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+500", "currency_symbol": "£", "currency_decimals": "2", "flag": "FK.png", "minLength": 7, "maxLength": 14}, {"capital": "King <PERSON> (Grytviken)", "citizenship": "of South Georgia and the South Sandwich Islands", "country_code": "239", "currency": "", "currency_code": "", "currency_sub_unit": "", "full_name": "South Georgia and the South Sandwich Islands", "iso_3166_2": "GS", "iso_3166_3": "SGS", "name": "South Georgia and the South Sandwich Islands", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+44", "currency_symbol": "£", "currency_decimals": "2", "flag": "GS.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Fijian", "country_code": "242", "currency": "Fiji dollar", "currency_code": "FJD", "currency_sub_unit": "cent", "full_name": "Republic of Fiji", "iso_3166_2": "FJ", "iso_3166_3": "FJI", "name": "Fiji", "region_code": "009", "sub_region_code": "054", "eea": false, "dial_code": "+679", "currency_symbol": "$", "currency_decimals": "2", "flag": "FJ.png", "minLength": 7, "maxLength": 14}, {"capital": "Helsinki", "citizenship": "Finnish", "country_code": "246", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Finland", "iso_3166_2": "FI", "iso_3166_3": "FIN", "name": "Finland", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+358", "currency_symbol": "€", "currency_decimals": "2", "flag": "FI.png", "minLength": 7, "maxLength": 14}, {"capital": "Paris", "citizenship": "French", "country_code": "250", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "French Republic", "iso_3166_2": "FR", "iso_3166_3": "FRA", "name": "France", "region_code": "150", "sub_region_code": "155", "eea": true, "dial_code": "+33", "currency_symbol": "€", "currency_decimals": "2", "flag": "FR.png", "minLength": 7, "maxLength": 14}, {"capital": "Cayenne", "citizenship": "<PERSON><PERSON><PERSON><PERSON>", "country_code": "254", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "French Guiana", "iso_3166_2": "GF", "iso_3166_3": "GUF", "name": "French Guiana", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+594", "currency_symbol": "€", "currency_decimals": "2", "flag": "GF.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>", "citizenship": "Polynesian", "country_code": "258", "currency": "CFP franc", "currency_code": "XPF", "currency_sub_unit": "centime", "full_name": "French Polynesia", "iso_3166_2": "PF", "iso_3166_3": "PYF", "name": "French Polynesia", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+689", "currency_symbol": "XPF", "currency_decimals": "0", "flag": "PF.png", "minLength": 7, "maxLength": 14}, {"capital": "Port-aux-Francais", "citizenship": "of French Southern and Antarctic Lands", "country_code": "260", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "French Southern and Antarctic Lands", "iso_3166_2": "TF", "iso_3166_3": "ATF", "name": "French Southern Territories", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+33", "currency_symbol": "€", "currency_decimals": "2", "flag": "TF.png", "minLength": 7, "maxLength": 14}, {"capital": "Djibouti", "citizenship": "Djiboutian", "country_code": "262", "currency": "Djibouti franc", "currency_code": "DJF", "currency_sub_unit": "", "full_name": "Republic of Djibouti", "iso_3166_2": "DJ", "iso_3166_3": "DJI", "name": "Djibouti", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+253", "currency_symbol": "DJF", "currency_decimals": "0", "flag": "DJ.png", "minLength": 7, "maxLength": 14}, {"capital": "Libreville", "citizenship": "Gabonese", "country_code": "266", "currency": "CFA franc (BEAC)", "currency_code": "XAF", "currency_sub_unit": "centime", "full_name": "Gabonese Republic", "iso_3166_2": "GA", "iso_3166_3": "GAB", "name": "Gabon", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+241", "currency_symbol": "FCF", "currency_decimals": "0", "flag": "GA.png", "minLength": 7, "maxLength": 14}, {"capital": "Tbilisi", "citizenship": "Georgian", "country_code": "268", "currency": "lari", "currency_code": "GEL", "currency_sub_unit": "tetri (inv.)", "full_name": "Georgia", "iso_3166_2": "GE", "iso_3166_3": "GEO", "name": "Georgia", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+995", "currency_symbol": "GEL", "currency_decimals": "2", "flag": "GE.png", "minLength": 7, "maxLength": 14}, {"capital": "Banjul", "citizenship": "Gambian", "country_code": "270", "currency": "<PERSON><PERSON><PERSON> (inv.)", "currency_code": "GMD", "currency_sub_unit": "butut", "full_name": "Republic of the Gambia", "iso_3166_2": "GM", "iso_3166_3": "GMB", "name": "Gambia", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+220", "currency_symbol": "D", "currency_decimals": "2", "flag": "GM.png", "minLength": 7, "maxLength": 14}, {"citizenship": "Palestinian", "country_code": "275", "iso_3166_2": "PS", "iso_3166_3": "PSE", "name": "Palestinian Territory, Occupied", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+970", "currency_symbol": "₪", "currency_decimals": "2", "flag": "PS.png", "minLength": 7, "maxLength": 14}, {"capital": "Berlin", "citizenship": "German", "country_code": "276", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Federal Republic of Germany", "iso_3166_2": "DE", "iso_3166_3": "DEU", "name": "Germany", "region_code": "150", "sub_region_code": "155", "eea": true, "dial_code": "+49", "currency_symbol": "€", "currency_decimals": "2", "flag": "DE.png", "minLength": 7, "maxLength": 14}, {"capital": "Accra", "citizenship": "Ghanaian", "country_code": "288", "currency": "Ghana cedi", "currency_code": "GHS", "currency_sub_unit": "pesewa", "full_name": "Republic of Ghana", "iso_3166_2": "GH", "iso_3166_3": "GHA", "name": "Ghana", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+233", "currency_symbol": "¢", "currency_decimals": "2", "flag": "GH.png", "minLength": 7, "maxLength": 14}, {"capital": "Gibraltar", "citizenship": "Gibraltarian", "country_code": "292", "currency": "Gibraltar pound", "currency_code": "GIP", "currency_sub_unit": "penny", "full_name": "Gibraltar", "iso_3166_2": "GI", "iso_3166_3": "GIB", "name": "Gibraltar", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+350", "currency_symbol": "£", "currency_decimals": "2", "flag": "GI.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Kiribatian", "country_code": "296", "currency": "Australian dollar", "currency_code": "AUD", "currency_sub_unit": "cent", "full_name": "Republic of Kiribati", "iso_3166_2": "KI", "iso_3166_3": "KIR", "name": "Kiribati", "region_code": "009", "sub_region_code": "057", "eea": false, "dial_code": "+686", "currency_symbol": "$", "currency_decimals": "2", "flag": "KI.png", "minLength": 7, "maxLength": 14}, {"capital": "Athens", "citizenship": "Greek", "country_code": "300", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Hellenic Republic", "iso_3166_2": "GR", "iso_3166_3": "GRC", "name": "Greece", "region_code": "150", "sub_region_code": "039", "eea": true, "dial_code": "+30", "currency_symbol": "€", "currency_decimals": "2", "flag": "GR.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>", "citizenship": "<PERSON>er", "country_code": "304", "currency": "Danish krone", "currency_code": "DKK", "currency_sub_unit": "øre (inv.)", "full_name": "Greenland", "iso_3166_2": "GL", "iso_3166_3": "GRL", "name": "Greenland", "region_code": "019", "sub_region_code": "021", "eea": false, "dial_code": "+299", "currency_symbol": "kr", "currency_decimals": "2", "flag": "GL.png", "minLength": 7, "maxLength": 14}, {"capital": "St George’s", "citizenship": "Grenadian", "country_code": "308", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "Grenada", "iso_3166_2": "GD", "iso_3166_3": "GRD", "name": "Grenada", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "GD.png", "minLength": 7, "maxLength": 14}, {"capital": "Basse Terre", "citizenship": "Guadeloupean", "country_code": "312", "currency": "euro", "currency_code": "EUR ", "currency_sub_unit": "cent", "full_name": "Guadeloupe", "iso_3166_2": "GP", "iso_3166_3": "GLP", "name": "Guadeloupe", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+590", "currency_symbol": "€", "currency_decimals": "2", "flag": "GP.png", "minLength": 7, "maxLength": 14}, {"capital": "Agaña (Hagåtña)", "citizenship": "Guamanian", "country_code": "316", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Territory of Guam", "iso_3166_2": "GU", "iso_3166_3": "GUM", "name": "Guam", "region_code": "009", "sub_region_code": "057", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "GU.png", "minLength": 7, "maxLength": 14}, {"capital": "Guatemala City", "citizenship": "Guatemalan", "country_code": "320", "currency": "quetzal (pl. quetzales)", "currency_code": "GTQ", "currency_sub_unit": "centavo", "full_name": "Republic of Guatemala", "iso_3166_2": "GT", "iso_3166_3": "GTM", "name": "Guatemala", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+502", "currency_symbol": "Q", "currency_decimals": "2", "flag": "GT.png", "minLength": 7, "maxLength": 14}, {"capital": "Conakry", "citizenship": "Guinean", "country_code": "324", "currency": "Guinean franc", "currency_code": "GNF", "currency_sub_unit": "", "full_name": "Republic of Guinea", "iso_3166_2": "GN", "iso_3166_3": "GIN", "name": "Guinea", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+224", "currency_symbol": "GNF", "currency_decimals": "0", "flag": "GN.png", "minLength": 7, "maxLength": 14}, {"capital": "Georgetown", "citizenship": "Guyanese", "country_code": "328", "currency": "Guyana dollar", "currency_code": "GYD", "currency_sub_unit": "cent", "full_name": "Cooperative Republic of Guyana", "iso_3166_2": "GY", "iso_3166_3": "GUY", "name": "Guyana", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+592", "currency_symbol": "$", "currency_decimals": "2", "flag": "GY.png", "minLength": 7, "maxLength": 14}, {"capital": "Port-au-Prince", "citizenship": "Haitian", "country_code": "332", "currency": "gourde", "currency_code": "HTG", "currency_sub_unit": "centime", "full_name": "Republic of Haiti", "iso_3166_2": "HT", "iso_3166_3": "HTI", "name": "Haiti", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+509", "currency_symbol": "G", "currency_decimals": "2", "flag": "HT.png", "minLength": 7, "maxLength": 14}, {"capital": "Territory of Heard Island and McDonald Islands", "citizenship": "of Territory of Heard Island and McDonald Islands", "country_code": "334", "currency": "", "currency_code": "", "currency_sub_unit": "", "full_name": "Territory of Heard Island and McDonald Islands", "iso_3166_2": "HM", "iso_3166_3": "HMD", "name": "Heard Island and McDonald Islands", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+61", "currency_symbol": "$", "currency_decimals": "2", "flag": "HM.png", "minLength": 7, "maxLength": 14}, {"capital": "Vatican City", "citizenship": "of the Holy See/of the Vatican", "country_code": "336", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "the Holy See/ Vatican City State", "iso_3166_2": "VA", "iso_3166_3": "VAT", "name": "Holy See (Vatican City State)", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+39", "currency_symbol": "€", "currency_decimals": "2", "flag": "VA.png", "minLength": 7, "maxLength": 14}, {"capital": "Tegucigalpa", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "340", "currency": "<PERSON><PERSON><PERSON>", "currency_code": "HNL", "currency_sub_unit": "centavo", "full_name": "Republic of Honduras", "iso_3166_2": "HN", "iso_3166_3": "HND", "name": "Honduras", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+504", "currency_symbol": "L", "currency_decimals": "2", "flag": "HN.png", "minLength": 7, "maxLength": 14}, {"capital": "(HK3)", "citizenship": "Hong Kong Chinese", "country_code": "344", "currency": "Hong Kong dollar", "currency_code": "HKD", "currency_sub_unit": "cent", "full_name": "Hong Kong Special Administrative Region of the People’s Republic of China (HK2)", "iso_3166_2": "HK", "iso_3166_3": "HKG", "name": "Hong Kong", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+852", "currency_symbol": "$", "currency_decimals": "2", "flag": "HK.png", "minLength": 7, "maxLength": 14}, {"capital": "Budapest", "citizenship": "Hungarian", "country_code": "348", "currency": "forint (inv.)", "currency_code": "HUF", "currency_sub_unit": "(fill<PERSON><PERSON> (inv.))", "full_name": "Republic of Hungary", "iso_3166_2": "HU", "iso_3166_3": "HUN", "name": "Hungary", "region_code": "150", "sub_region_code": "151", "eea": true, "dial_code": "+36", "currency_symbol": "Ft", "currency_decimals": "2", "flag": "HU.png", "minLength": 7, "maxLength": 14}, {"capital": "Reykjavik", "citizenship": "<PERSON><PERSON>", "country_code": "352", "currency": "króna (pl. kr<PERSON>ur)", "currency_code": "ISK", "currency_sub_unit": "", "full_name": "Republic of Iceland", "iso_3166_2": "IS", "iso_3166_3": "ISL", "name": "Iceland", "region_code": "150", "sub_region_code": "154", "eea": false, "dial_code": "+354", "currency_symbol": "kr", "currency_decimals": "0", "flag": "IS.png", "minLength": 7, "maxLength": 14}, {"capital": "New Delhi", "citizenship": "Indian", "country_code": "356", "currency": "Indian rupee", "currency_code": "INR", "currency_sub_unit": "paisa", "full_name": "Republic of India", "iso_3166_2": "IN", "iso_3166_3": "IND", "name": "India", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+91", "currency_symbol": "₹", "currency_decimals": "2", "flag": "IN.png", "minLength": 7, "maxLength": 14}, {"capital": "Jakarta", "citizenship": "Indonesian", "country_code": "360", "currency": "Indonesian rupiah (inv.)", "currency_code": "IDR", "currency_sub_unit": "sen (inv.)", "full_name": "Republic of Indonesia", "iso_3166_2": "ID", "iso_3166_3": "IDN", "name": "Indonesia", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+62", "currency_symbol": "Rp", "currency_decimals": "2", "flag": "ID.png", "minLength": 7, "maxLength": 14}, {"capital": "Tehran", "citizenship": "Iranian", "country_code": "364", "currency": "Iranian rial", "currency_code": "IRR", "currency_sub_unit": "(dinar) (IR1)", "full_name": "Islamic Republic of Iran", "iso_3166_2": "IR", "iso_3166_3": "IRN", "name": "Iran, Islamic Republic of", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+98", "currency_symbol": "﷼", "currency_decimals": "2", "flag": "IR.png", "minLength": 7, "maxLength": 14}, {"capital": "Baghdad", "citizenship": "Iraqi", "country_code": "368", "currency": "Iraqi dinar", "currency_code": "IQD", "currency_sub_unit": "fils (inv.)", "full_name": "Republic of Iraq", "iso_3166_2": "IQ", "iso_3166_3": "IRQ", "name": "Iraq", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+964", "currency_symbol": "IQD", "currency_decimals": "3", "flag": "IQ.png", "minLength": 7, "maxLength": 14}, {"capital": "Dublin", "citizenship": "Irish", "country_code": "372", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Ireland (IE1)", "iso_3166_2": "IE", "iso_3166_3": "IRL", "name": "Ireland", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+353", "currency_symbol": "€", "currency_decimals": "2", "flag": "IE.png", "minLength": 7, "maxLength": 14}, {"capital": "(IL1)", "citizenship": "Israeli", "country_code": "376", "currency": "shekel", "currency_code": "ILS", "currency_sub_unit": "agora", "full_name": "State of Israel", "iso_3166_2": "IL", "iso_3166_3": "ISR", "name": "Israel", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+972", "currency_symbol": "₪", "currency_decimals": "2", "flag": "IL.png", "minLength": 7, "maxLength": 14}, {"capital": "Rome", "citizenship": "Italian", "country_code": "380", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Italian Republic", "iso_3166_2": "IT", "iso_3166_3": "ITA", "name": "Italy", "region_code": "150", "sub_region_code": "039", "eea": true, "dial_code": "+39", "currency_symbol": "€", "currency_decimals": "2", "flag": "IT.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CI1)", "citizenship": "Ivorian", "country_code": "384", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Republic ofCôte d'Ivoire", "iso_3166_2": "CI", "iso_3166_3": "CIV", "name": "Côte d'Ivoire", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+225", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "CI.png", "minLength": 7, "maxLength": 14}, {"capital": "Kingston", "citizenship": "Jamaican", "country_code": "388", "currency": "Jamaica dollar", "currency_code": "JMD", "currency_sub_unit": "cent", "full_name": "Jamaica", "iso_3166_2": "JM", "iso_3166_3": "JAM", "name": "Jamaica", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "JM.png", "minLength": 7, "maxLength": 14}, {"capital": "Tokyo", "citizenship": "Japanese", "country_code": "392", "currency": "yen (inv.)", "currency_code": "JPY", "currency_sub_unit": "(sen (inv.)) (JP1)", "full_name": "Japan", "iso_3166_2": "JP", "iso_3166_3": "JPN", "name": "Japan", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+81", "currency_symbol": "¥", "currency_decimals": "0", "flag": "JP.png", "minLength": 7, "maxLength": 14}, {"capital": "Astana", "citizenship": "Kazakh", "country_code": "398", "currency": "tenge (inv.)", "currency_code": "KZT", "currency_sub_unit": "tiyn", "full_name": "Republic of Kazakhstan", "iso_3166_2": "KZ", "iso_3166_3": "KAZ", "name": "Kazakhstan", "region_code": "142", "sub_region_code": "143", "eea": false, "dial_code": "+7", "currency_symbol": "лв", "currency_decimals": "2", "flag": "KZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Amman", "citizenship": "<PERSON><PERSON>", "country_code": "400", "currency": "Jordanian dinar", "currency_code": "JOD", "currency_sub_unit": "100 qirsh", "full_name": "Hashemite Kingdom of Jordan", "iso_3166_2": "JO", "iso_3166_3": "JOR", "name": "Jordan", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+962", "currency_symbol": "JOD", "currency_decimals": "2", "flag": "JO.png", "minLength": 7, "maxLength": 14}, {"capital": "Nairobi", "citizenship": "Kenyan", "country_code": "404", "currency": "Kenyan shilling", "currency_code": "KES", "currency_sub_unit": "cent", "full_name": "Republic of Kenya", "iso_3166_2": "KE", "iso_3166_3": "KEN", "name": "Kenya", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+254", "currency_symbol": "KES", "currency_decimals": "2", "flag": "KE.png", "minLength": 7, "maxLength": 14}, {"capital": "Pyongyang", "citizenship": "North Korean", "country_code": "408", "currency": "North Korean won (inv.)", "currency_code": "KPW", "currency_sub_unit": "chun (inv.)", "full_name": "Democratic People’s Republic of Korea", "iso_3166_2": "KP", "iso_3166_3": "PRK", "name": "Korea, Democratic People's Republic of", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+850", "currency_symbol": "₩", "currency_decimals": "2", "flag": "KP.png", "minLength": 7, "maxLength": 14}, {"capital": "Seoul", "citizenship": "South Korean", "country_code": "410", "currency": "South Korean won (inv.)", "currency_code": "KRW", "currency_sub_unit": "(chun (inv.))", "full_name": "Republic of Korea", "iso_3166_2": "KR", "iso_3166_3": "KOR", "name": "Korea, Republic of", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+82", "currency_symbol": "₩", "currency_decimals": "0", "flag": "KR.png", "minLength": 7, "maxLength": 14}, {"capital": "Kuwait City", "citizenship": "Kuwaiti", "country_code": "414", "currency": "Kuwaiti dinar", "currency_code": "KWD", "currency_sub_unit": "fils (inv.)", "full_name": "State of Kuwait", "iso_3166_2": "KW", "iso_3166_3": "KWT", "name": "Kuwait", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+965", "currency_symbol": "KWD", "currency_decimals": "3", "flag": "KW.png", "minLength": 7, "maxLength": 14}, {"capital": "Bishkek", "citizenship": "Kyrgyz", "country_code": "417", "currency": "som", "currency_code": "KGS", "currency_sub_unit": "tyiyn", "full_name": "Kyrgyz Republic", "iso_3166_2": "KG", "iso_3166_3": "KGZ", "name": "Kyrgyzstan", "region_code": "142", "sub_region_code": "143", "eea": false, "dial_code": "+996", "currency_symbol": "лв", "currency_decimals": "2", "flag": "KG.png", "minLength": 7, "maxLength": 14}, {"capital": "Vientiane", "citizenship": "Lao", "country_code": "418", "currency": "kip (inv.)", "currency_code": "LAK", "currency_sub_unit": "(at (inv.))", "full_name": "Lao People’s Democratic Republic", "iso_3166_2": "LA", "iso_3166_3": "LAO", "name": "Lao People's Democratic Republic", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+856", "currency_symbol": "₭", "currency_decimals": "0", "flag": "LA.png", "minLength": 7, "maxLength": 14}, {"capital": "Beirut", "citizenship": "Lebanese", "country_code": "422", "currency": "Lebanese pound", "currency_code": "LBP", "currency_sub_unit": "(piastre)", "full_name": "Lebanese Republic", "iso_3166_2": "LB", "iso_3166_3": "LBN", "name": "Lebanon", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+961", "currency_symbol": "£", "currency_decimals": "2", "flag": "LB.png", "minLength": 7, "maxLength": 14}, {"capital": "Maseru", "citizenship": "Basotho", "country_code": "426", "currency": "loti (pl. maloti)", "currency_code": "LSL", "currency_sub_unit": "sente", "full_name": "Kingdom of Lesotho", "iso_3166_2": "LS", "iso_3166_3": "LSO", "name": "Lesotho", "region_code": "002", "sub_region_code": "018", "eea": false, "dial_code": "+266", "currency_symbol": "L", "currency_decimals": "2", "flag": "LS.png", "minLength": 7, "maxLength": 14}, {"capital": "Riga", "citizenship": "Latvian", "country_code": "428", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Latvia", "iso_3166_2": "LV", "iso_3166_3": "LVA", "name": "Latvia", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+371", "currency_symbol": "Ls", "currency_decimals": "2", "flag": "LV.png", "minLength": 7, "maxLength": 14}, {"capital": "Monrovia", "citizenship": "Liberian", "country_code": "430", "currency": "Liberian dollar", "currency_code": "LRD", "currency_sub_unit": "cent", "full_name": "Republic of Liberia", "iso_3166_2": "LR", "iso_3166_3": "LBR", "name": "Liberia", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+231", "currency_symbol": "$", "currency_decimals": "2", "flag": "LR.png", "minLength": 7, "maxLength": 14}, {"capital": "Tripoli", "citizenship": "Libyan", "country_code": "434", "currency": "Libyan dinar", "currency_code": "LYD", "currency_sub_unit": "dirham", "full_name": "Socialist People’s Libyan Arab Jamahiriya", "iso_3166_2": "LY", "iso_3166_3": "LBY", "name": "Libya", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+218", "currency_symbol": "LYD", "currency_decimals": "3", "flag": "LY.png", "minLength": 7, "maxLength": 14}, {"capital": "Vaduz", "citizenship": "Liechtensteiner", "country_code": "438", "currency": "Swiss franc", "currency_code": "CHF", "currency_sub_unit": "centime", "full_name": "Principality of Liechtenstein", "iso_3166_2": "LI", "iso_3166_3": "LIE", "name": "Liechtenstein", "region_code": "150", "sub_region_code": "155", "eea": false, "dial_code": "+423", "currency_symbol": "CHF", "currency_decimals": "2", "flag": "LI.png", "minLength": 7, "maxLength": 14}, {"capital": "Vilnius", "citizenship": "Lithuanian", "country_code": "440", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Lithuania", "iso_3166_2": "LT", "iso_3166_3": "LTU", "name": "Lithuania", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+370", "currency_symbol": "Lt", "currency_decimals": "2", "flag": "LT.png", "minLength": 7, "maxLength": 14}, {"capital": "Luxembourg", "citizenship": "<PERSON>er", "country_code": "442", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Grand Duchy of Luxembourg", "iso_3166_2": "LU", "iso_3166_3": "LUX", "name": "Luxembourg", "region_code": "150", "sub_region_code": "155", "eea": true, "dial_code": "+352", "currency_symbol": "€", "currency_decimals": "2", "flag": "LU.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON> (MO3)", "citizenship": "Macanese", "country_code": "446", "currency": "pataca", "currency_code": "MOP", "currency_sub_unit": "avo", "full_name": "Macao Special Administrative Region of the People’s Republic of China (MO2)", "iso_3166_2": "MO", "iso_3166_3": "MAC", "name": "Macao", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+853", "currency_symbol": "MOP", "currency_decimals": "2", "flag": "MO.png", "minLength": 7, "maxLength": 14}, {"capital": "Antananarivo", "citizenship": "Malagasy", "country_code": "450", "currency": "ariary", "currency_code": "MGA", "currency_sub_unit": "iraimbilanja (inv.)", "full_name": "Republic of Madagascar", "iso_3166_2": "MG", "iso_3166_3": "MDG", "name": "Madagascar", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+261", "currency_symbol": "MGA", "currency_decimals": "2", "flag": "MG.png", "minLength": 7, "maxLength": 14}, {"capital": "Lilongwe", "citizenship": "Malawian", "country_code": "454", "currency": "<PERSON><PERSON> kwacha (inv.)", "currency_code": "MWK", "currency_sub_unit": "tambala (inv.)", "full_name": "Republic of Malawi", "iso_3166_2": "MW", "iso_3166_3": "MWI", "name": "Malawi", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+265", "currency_symbol": "MK", "currency_decimals": "2", "flag": "MW.png", "minLength": 7, "maxLength": 14}, {"capital": "Kuala Lumpur (MY1)", "citizenship": "Malaysian", "country_code": "458", "currency": "ringgit (inv.)", "currency_code": "MYR", "currency_sub_unit": "sen (inv.)", "full_name": "Malaysia", "iso_3166_2": "MY", "iso_3166_3": "MYS", "name": "Malaysia", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+60", "currency_symbol": "RM", "currency_decimals": "2", "flag": "MY.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Maldivian", "country_code": "462", "currency": "rufiyaa", "currency_code": "MVR", "currency_sub_unit": "laari (inv.)", "full_name": "Republic of Maldives", "iso_3166_2": "MV", "iso_3166_3": "MDV", "name": "Maldives", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+960", "currency_symbol": "Rf", "currency_decimals": "2", "flag": "MV.png", "minLength": 7, "maxLength": 14}, {"capital": "Ba<PERSON><PERSON>", "citizenship": "<PERSON><PERSON>", "country_code": "466", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Republic of Mali", "iso_3166_2": "ML", "iso_3166_3": "MLI", "name": "Mali", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+223", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "ML.png", "minLength": 7, "maxLength": 14}, {"capital": "Valletta", "citizenship": "Maltese", "country_code": "470", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Malta", "iso_3166_2": "MT", "iso_3166_3": "MLT", "name": "Malta", "region_code": "150", "sub_region_code": "039", "eea": true, "dial_code": "+356", "currency_symbol": "MTL", "currency_decimals": "2", "flag": "MT.png", "minLength": 7, "maxLength": 14}, {"capital": "Fort-de-France", "citizenship": "Martinican", "country_code": "474", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Martinique", "iso_3166_2": "MQ", "iso_3166_3": "MTQ", "name": "Martinique", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+596", "currency_symbol": "€", "currency_decimals": "2", "flag": "MQ.png", "minLength": 7, "maxLength": 14}, {"capital": "Nouakchott", "citizenship": "Mauritanian", "country_code": "478", "currency": "ougu<PERSON>", "currency_code": "MRO", "currency_sub_unit": "<PERSON><PERSON><PERSON>", "full_name": "Islamic Republic of Mauritania", "iso_3166_2": "MR", "iso_3166_3": "MRT", "name": "Mauritania", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+222", "currency_symbol": "UM", "currency_decimals": "2", "flag": "MR.png", "minLength": 7, "maxLength": 14}, {"capital": "Port Louis", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "480", "currency": "Mauritian rupee", "currency_code": "MUR", "currency_sub_unit": "cent", "full_name": "Republic of Mauritius", "iso_3166_2": "MU", "iso_3166_3": "MUS", "name": "Mauritius", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+230", "currency_symbol": "₨", "currency_decimals": "2", "flag": "MU.png", "minLength": 7, "maxLength": 14}, {"capital": "Mexico City", "citizenship": "Mexican", "country_code": "484", "currency": "Mexican peso", "currency_code": "MXN", "currency_sub_unit": "centavo", "full_name": "United Mexican States", "iso_3166_2": "MX", "iso_3166_3": "MEX", "name": "Mexico", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+52", "currency_symbol": "$", "currency_decimals": "2", "flag": "MX.png", "minLength": 7, "maxLength": 14}, {"capital": "Monaco", "citizenship": "Monegasque", "country_code": "492", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Principality of Monaco", "iso_3166_2": "MC", "iso_3166_3": "MCO", "name": "Monaco", "region_code": "150", "sub_region_code": "155", "eea": false, "dial_code": "+377", "currency_symbol": "€", "currency_decimals": "2", "flag": "MC.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Mongolian", "country_code": "496", "currency": "<PERSON><PERSON>", "currency_code": "MNT", "currency_sub_unit": "möngö (inv.)", "full_name": "Mongolia", "iso_3166_2": "MN", "iso_3166_3": "MNG", "name": "Mongolia", "region_code": "142", "sub_region_code": "030", "eea": false, "dial_code": "+976", "currency_symbol": "₮", "currency_decimals": "2", "flag": "MN.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>", "citizenship": "Moldovan", "country_code": "498", "currency": "Moldovan leu (pl. lei)", "currency_code": "MDL", "currency_sub_unit": "ban", "full_name": "Republic of Moldova", "iso_3166_2": "MD", "iso_3166_3": "MDA", "name": "Moldova, Republic of", "region_code": "150", "sub_region_code": "151", "eea": false, "dial_code": "+373", "currency_symbol": "MDL", "currency_decimals": "2", "flag": "MD.png", "minLength": 7, "maxLength": 14}, {"capital": "Podgorica", "citizenship": "Montenegrin", "country_code": "499", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Montenegro", "iso_3166_2": "ME", "iso_3166_3": "MNE", "name": "Montenegro", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+382", "currency_symbol": "€", "currency_decimals": "2", "flag": "ME.png", "minLength": 7, "maxLength": 14}, {"capital": "Plymouth (MS2)", "citizenship": "<PERSON><PERSON><PERSON><PERSON>", "country_code": "500", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "Montserrat", "iso_3166_2": "MS", "iso_3166_3": "MSR", "name": "Montserrat", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "MS.png", "minLength": 7, "maxLength": 14}, {"capital": "Rabat", "citizenship": "Moroccan", "country_code": "504", "currency": "Moroccan dirham", "currency_code": "MAD", "currency_sub_unit": "centime", "full_name": "Kingdom of Morocco", "iso_3166_2": "MA", "iso_3166_3": "MAR", "name": "Morocco", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+212", "currency_symbol": "MAD", "currency_decimals": "2", "flag": "MA.png", "minLength": 7, "maxLength": 14}, {"capital": "Maputo", "citizenship": "Mozambican", "country_code": "508", "currency": "metical", "currency_code": "MZN", "currency_sub_unit": "centavo", "full_name": "Republic of Mozambique", "iso_3166_2": "MZ", "iso_3166_3": "MOZ", "name": "Mozambique", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+258", "currency_symbol": "MT", "currency_decimals": "2", "flag": "MZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Muscat", "citizenship": "Omani", "country_code": "512", "currency": "<PERSON><PERSON> rial", "currency_code": "OMR", "currency_sub_unit": "baiza", "full_name": "Sultanate of Oman", "iso_3166_2": "OM", "iso_3166_3": "OMN", "name": "Oman", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+968", "currency_symbol": "﷼", "currency_decimals": "3", "flag": "OM.png", "minLength": 7, "maxLength": 14}, {"capital": "Windhoek", "citizenship": "Namibian", "country_code": "516", "currency": "Namibian dollar", "currency_code": "NAD", "currency_sub_unit": "cent", "full_name": "Republic of Namibia", "iso_3166_2": "NA", "iso_3166_3": "NAM", "name": "Namibia", "region_code": "002", "sub_region_code": "018", "eea": false, "dial_code": "+264", "currency_symbol": "$", "currency_decimals": "2", "flag": "NA.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Nauruan", "country_code": "520", "currency": "Australian dollar", "currency_code": "AUD", "currency_sub_unit": "cent", "full_name": "Republic of Nauru", "iso_3166_2": "NR", "iso_3166_3": "NRU", "name": "Nauru", "region_code": "009", "sub_region_code": "057", "eea": false, "dial_code": "+674", "currency_symbol": "$", "currency_decimals": "2", "flag": "NR.png", "minLength": 7, "maxLength": 14}, {"capital": "Kathman<PERSON>", "citizenship": "Nepalese", "country_code": "524", "currency": "Nepalese rupee", "currency_code": "NPR", "currency_sub_unit": "paisa (inv.)", "full_name": "Nepal", "iso_3166_2": "NP", "iso_3166_3": "NPL", "name": "Nepal", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+977", "currency_symbol": "₨", "currency_decimals": "2", "flag": "NP.png", "minLength": 7, "maxLength": 14}, {"capital": "Amsterdam (NL2)", "citizenship": "Dutch", "country_code": "528", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Kingdom of the Netherlands", "iso_3166_2": "NL", "iso_3166_3": "NLD", "name": "Netherlands", "region_code": "150", "sub_region_code": "155", "eea": true, "dial_code": "+31", "currency_symbol": "€", "currency_decimals": "2", "flag": "NL.png", "minLength": 7, "maxLength": 14}, {"capital": "Willem<PERSON>", "citizenship": "Curaçaoan", "country_code": "531", "currency": "Netherlands Antillean guilder (CW1)", "currency_code": "ANG", "currency_sub_unit": "cent", "full_name": "Curaçao", "iso_3166_2": "CW", "iso_3166_3": "CUW", "name": "Curaçao", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+599", "minLength": 7, "maxLength": 14}, {"capital": "Oranjestad", "citizenship": "Aruban", "country_code": "533", "currency": "Aruban guilder", "currency_code": "AWG", "currency_sub_unit": "cent", "full_name": "Aruba", "iso_3166_2": "AW", "iso_3166_3": "ABW", "name": "Aruba", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+297", "currency_symbol": "ƒ", "currency_decimals": "2", "flag": "AW.png", "minLength": 7, "maxLength": 14}, {"capital": "Nouméa", "citizenship": "New Caledonian", "country_code": "540", "currency": "CFP franc", "currency_code": "XPF", "currency_sub_unit": "centime", "full_name": "New Caledonia", "iso_3166_2": "NC", "iso_3166_3": "NCL", "name": "New Caledonia", "region_code": "009", "sub_region_code": "054", "eea": false, "dial_code": "+687", "currency_symbol": "XPF", "currency_decimals": "0", "flag": "NC.png", "minLength": 7, "maxLength": 14}, {"capital": "Port Vila", "citizenship": "Vanuatuan", "country_code": "548", "currency": "vatu (inv.)", "currency_code": "VUV", "currency_sub_unit": "", "full_name": "Republic of Vanuatu", "iso_3166_2": "VU", "iso_3166_3": "VUT", "name": "Vanuatu", "region_code": "009", "sub_region_code": "054", "eea": false, "dial_code": "+678", "currency_symbol": "Vt", "currency_decimals": "0", "flag": "VU.png", "minLength": 7, "maxLength": 14}, {"capital": "Wellington", "citizenship": "New Zealander", "country_code": "554", "currency": "New Zealand dollar", "currency_code": "NZD", "currency_sub_unit": "cent", "full_name": "New Zealand", "iso_3166_2": "NZ", "iso_3166_3": "NZL", "name": "New Zealand", "region_code": "009", "sub_region_code": "053", "eea": false, "dial_code": "+64", "currency_symbol": "$", "currency_decimals": "2", "flag": "NZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Managua", "citizenship": "Nicaraguan", "country_code": "558", "currency": "córdoba oro", "currency_code": "NIO", "currency_sub_unit": "centavo", "full_name": "Republic of Nicaragua", "iso_3166_2": "NI", "iso_3166_3": "NIC", "name": "Nicaragua", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+505", "currency_symbol": "C$", "currency_decimals": "2", "flag": "NI.png", "minLength": 7, "maxLength": 14}, {"capital": "Niamey", "citizenship": "Nigerien", "country_code": "562", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Republic of Niger", "iso_3166_2": "NE", "iso_3166_3": "NER", "name": "Niger", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+227", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "NE.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON>ja", "citizenship": "Nigerian", "country_code": "566", "currency": "naira (inv.)", "currency_code": "NGN", "currency_sub_unit": "kobo (inv.)", "full_name": "Federal Republic of Nigeria", "iso_3166_2": "NG", "iso_3166_3": "NGA", "name": "Nigeria", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+234", "currency_symbol": "₦", "currency_decimals": "2", "flag": "NG.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "570", "currency": "New Zealand dollar", "currency_code": "NZD", "currency_sub_unit": "cent", "full_name": "Niue", "iso_3166_2": "NU", "iso_3166_3": "NIU", "name": "Niue", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+683", "currency_symbol": "$", "currency_decimals": "2", "flag": "NU.png", "minLength": 7, "maxLength": 14}, {"capital": "Kingston", "citizenship": "Norfolk Islander", "country_code": "574", "currency": "Australian dollar", "currency_code": "AUD", "currency_sub_unit": "cent", "full_name": "Territory of Norfolk Island", "iso_3166_2": "NF", "iso_3166_3": "NFK", "name": "Norfolk Island", "region_code": "009", "sub_region_code": "053", "eea": false, "dial_code": "+672", "currency_symbol": "$", "currency_decimals": "2", "flag": "NF.png", "minLength": 7, "maxLength": 14}, {"capital": "Oslo", "citizenship": "Norwegian", "country_code": "578", "currency": "Norwegian krone (pl. kroner)", "currency_code": "NOK", "currency_sub_unit": "øre (inv.)", "full_name": "Kingdom of Norway", "iso_3166_2": "NO", "iso_3166_3": "NOR", "name": "Norway", "region_code": "150", "sub_region_code": "154", "eea": false, "dial_code": "+47", "currency_symbol": "kr", "currency_decimals": "2", "flag": "NO.png", "minLength": 7, "maxLength": 14}, {"capital": "Sai<PERSON>", "citizenship": "Northern Mariana Islander", "country_code": "580", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Commonwealth of the Northern Mariana Islands", "iso_3166_2": "MP", "iso_3166_3": "MNP", "name": "Northern Mariana Islands", "region_code": "009", "sub_region_code": "057", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "MP.png", "minLength": 7, "maxLength": 14}, {"capital": "United States Minor Outlying Islands", "citizenship": "of United States Minor Outlying Islands", "country_code": "581", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "United States Minor Outlying Islands", "iso_3166_2": "UM", "iso_3166_3": "UMI", "name": "United States Minor Outlying Islands", "region_code": "", "sub_region_code": "", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "UM.png", "minLength": 7, "maxLength": 14}, {"capital": "Palikir", "citizenship": "Micronesian", "country_code": "583", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Federated States of Micronesia", "iso_3166_2": "FM", "iso_3166_3": "FSM", "name": "Micronesia, Federated States of Micronesia", "region_code": "009", "sub_region_code": "057", "eea": false, "dial_code": "+691", "currency_symbol": "$", "currency_decimals": "2", "flag": "FM.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "<PERSON><PERSON>", "country_code": "584", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Republic of the Marshall Islands", "iso_3166_2": "MH", "iso_3166_3": "MHL", "name": "Marshall Islands", "region_code": "009", "sub_region_code": "057", "eea": false, "dial_code": "+692", "currency_symbol": "$", "currency_decimals": "2", "flag": "MH.png", "minLength": 7, "maxLength": 14}, {"capital": "Melekeok", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "585", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Republic of Palau", "iso_3166_2": "PW", "iso_3166_3": "PLW", "name": "<PERSON><PERSON>", "region_code": "009", "sub_region_code": "057", "eea": false, "dial_code": "+680", "currency_symbol": "$", "currency_decimals": "2", "flag": "PW.png", "minLength": 7, "maxLength": 14}, {"capital": "Islamabad", "citizenship": "Pakistani", "country_code": "586", "currency": "Pakistani rupee", "currency_code": "PKR", "currency_sub_unit": "paisa", "full_name": "Islamic Republic of Pakistan", "iso_3166_2": "PK", "iso_3166_3": "PAK", "name": "Pakistan", "region_code": "142", "sub_region_code": "034", "eea": false, "dial_code": "+92", "currency_symbol": "₨", "currency_decimals": "2", "flag": "PK.png", "minLength": 7, "maxLength": 14}, {"capital": "Panama City", "citizenship": "Panamanian", "country_code": "591", "currency": "balboa", "currency_code": "PAB", "currency_sub_unit": "centésimo", "full_name": "Republic of Panama", "iso_3166_2": "PA", "iso_3166_3": "PAN", "name": "Panama", "region_code": "019", "sub_region_code": "013", "eea": false, "dial_code": "+507", "currency_symbol": "B/.", "currency_decimals": "2", "flag": "PA.png", "minLength": 7, "maxLength": 14}, {"capital": "Port Moresby", "citizenship": "Papua New Guinean", "country_code": "598", "currency": "kina (inv.)", "currency_code": "PGK", "currency_sub_unit": "toea (inv.)", "full_name": "Independent State of Papua New Guinea", "iso_3166_2": "PG", "iso_3166_3": "PNG", "name": "Papua New Guinea", "region_code": "009", "sub_region_code": "054", "eea": false, "dial_code": "+675", "currency_symbol": "PGK", "currency_decimals": "2", "flag": "PG.png", "minLength": 7, "maxLength": 14}, {"capital": "Asunción", "citizenship": "Paraguayan", "country_code": "600", "currency": "guaraní", "currency_code": "PYG", "currency_sub_unit": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "Republic of Paraguay", "iso_3166_2": "PY", "iso_3166_3": "PRY", "name": "Paraguay", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+595", "currency_symbol": "Gs", "currency_decimals": "0", "flag": "PY.png", "minLength": 7, "maxLength": 14}, {"capital": "Lima", "citizenship": "Peruvian", "country_code": "604", "currency": "new sol", "currency_code": "PEN", "currency_sub_unit": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "Republic of Peru", "iso_3166_2": "PE", "iso_3166_3": "PER", "name": "Peru", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+51", "currency_symbol": "S/.", "currency_decimals": "2", "flag": "PE.png", "minLength": 7, "maxLength": 14}, {"capital": "Manila", "citizenship": "Filipino", "country_code": "608", "currency": "Philippine peso", "currency_code": "PHP", "currency_sub_unit": "centavo", "full_name": "Republic of the Philippines", "iso_3166_2": "PH", "iso_3166_3": "PHL", "name": "Philippines", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+63", "currency_symbol": "Php", "currency_decimals": "2", "flag": "PH.png", "minLength": 7, "maxLength": 14}, {"capital": "Adamstown", "citizenship": "<PERSON><PERSON><PERSON><PERSON>", "country_code": "612", "currency": "New Zealand dollar", "currency_code": "NZD", "currency_sub_unit": "cent", "full_name": "Pitcairn Islands", "iso_3166_2": "PN", "iso_3166_3": "PCN", "name": "Pitcairn", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+649", "currency_symbol": "$", "currency_decimals": "2", "flag": "PN.png", "minLength": 7, "maxLength": 14}, {"capital": "Warsaw", "citizenship": "Polish", "country_code": "616", "currency": "<PERSON><PERSON><PERSON>", "currency_code": "PLN", "currency_sub_unit": "grosz (pl. groszy)", "full_name": "Republic of Poland", "iso_3166_2": "PL", "iso_3166_3": "POL", "name": "Poland", "region_code": "150", "sub_region_code": "151", "eea": true, "dial_code": "+48", "currency_symbol": "zł", "currency_decimals": "2", "flag": "PL.png", "minLength": 7, "maxLength": 14}, {"capital": "Lisbon", "citizenship": "Portuguese", "country_code": "620", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Portuguese Republic", "iso_3166_2": "PT", "iso_3166_3": "PRT", "name": "Portugal", "region_code": "150", "sub_region_code": "039", "eea": true, "dial_code": "+351", "currency_symbol": "€", "currency_decimals": "2", "flag": "PT.png", "minLength": 7, "maxLength": 14}, {"capital": "Bissau", "citizenship": "Guinea-Bissau national", "country_code": "624", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Republic of Guinea-Bissau", "iso_3166_2": "GW", "iso_3166_3": "GNB", "name": "Guinea-Bissau", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+245", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "GW.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "East Timorese", "country_code": "626", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Democratic Republic of East Timor", "iso_3166_2": "TL", "iso_3166_3": "TLS", "name": "Timor-Leste", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+670", "currency_symbol": "$", "currency_decimals": "2", "flag": "TL.png", "minLength": 7, "maxLength": 14}, {"capital": "San Juan", "citizenship": "Puerto Rican", "country_code": "630", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Commonwealth of Puerto Rico", "iso_3166_2": "PR", "iso_3166_3": "PRI", "name": "Puerto Rico", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "PR.png", "minLength": 7, "maxLength": 14}, {"capital": "Doha", "citizenship": "Qatari", "country_code": "634", "currency": "Qatari riyal", "currency_code": "QAR", "currency_sub_unit": "dirham", "full_name": "State of Qatar", "iso_3166_2": "QA", "iso_3166_3": "QAT", "name": "Qatar", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+974", "currency_symbol": "﷼", "currency_decimals": "2", "flag": "QA.png", "minLength": 7, "maxLength": 14}, {"capital": "Saint-Denis", "citizenship": "Reunionese", "country_code": "638", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Reunion", "iso_3166_2": "RE", "iso_3166_3": "REU", "name": "Reunion", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+262", "currency_symbol": "€", "currency_decimals": "2", "flag": "RE.png", "minLength": 7, "maxLength": 14}, {"capital": "Bucharest", "citizenship": "Romanian", "country_code": "642", "currency": "Romanian leu (pl. lei)", "currency_code": "RON", "currency_sub_unit": "ban (pl. bani)", "full_name": "Romania", "iso_3166_2": "RO", "iso_3166_3": "ROU", "name": "Romania", "region_code": "150", "sub_region_code": "151", "eea": true, "dial_code": "+40", "currency_symbol": "lei", "currency_decimals": "2", "flag": "RO.png", "minLength": 7, "maxLength": 14}, {"capital": "Moscow", "citizenship": "Russian", "country_code": "643", "currency": "Russian rouble", "currency_code": "RUB", "currency_sub_unit": "kopek", "full_name": "Russian Federation", "iso_3166_2": "RU", "iso_3166_3": "RUS", "name": "Russian Federation", "region_code": "150", "sub_region_code": "151", "eea": false, "dial_code": "+7", "currency_symbol": "руб", "currency_decimals": "2", "flag": "RU.png", "minLength": 7, "maxLength": 14}, {"capital": "Kigali", "citizenship": "Rwandan; Rwandese", "country_code": "646", "currency": "Rwandese franc", "currency_code": "RWF", "currency_sub_unit": "centime", "full_name": "Republic of Rwanda", "iso_3166_2": "RW", "iso_3166_3": "RWA", "name": "Rwanda", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+250", "currency_symbol": "RWF", "currency_decimals": "0", "flag": "RW.png", "minLength": 7, "maxLength": 14}, {"capital": "Gustavia", "citizenship": "of Saint <PERSON>", "country_code": "652", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Collectivity of Saint Barthelemy", "iso_3166_2": "BL", "iso_3166_3": "BLM", "name": "<PERSON>", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+590", "minLength": 7, "maxLength": 14}, {"capital": "Jamestown", "citizenship": "<PERSON> <PERSON>", "country_code": "654", "currency": "Saint Helena pound", "currency_code": "SHP", "currency_sub_unit": "penny", "full_name": "Saint Helena, Ascension and Tristan <PERSON>ha", "iso_3166_2": "SH", "iso_3166_3": "SHN", "name": "Saint Helena, Ascension and Tristan <PERSON>ha", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+290", "currency_symbol": "£", "currency_decimals": "2", "flag": "SH.png", "minLength": 7, "maxLength": 14}, {"capital": "Basseterre", "citizenship": "Kittsian; Nevisian", "country_code": "659", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "Federation of Saint Kitts and Nevis", "iso_3166_2": "KN", "iso_3166_3": "KNA", "name": "Saint Kitts and Nevis", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "KN.png", "minLength": 7, "maxLength": 14}, {"capital": "The Valley", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "660", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "<PERSON><PERSON><PERSON>", "iso_3166_2": "AI", "iso_3166_3": "AIA", "name": "<PERSON><PERSON><PERSON>", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "AI.png", "minLength": 7, "maxLength": 14}, {"capital": "Castries", "citizenship": "Saint Lucian", "country_code": "662", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "Saint Lucia", "iso_3166_2": "LC", "iso_3166_3": "LCA", "name": "Saint Lucia", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "LC.png", "minLength": 7, "maxLength": 14}, {"capital": "Marigot", "citizenship": "of Saint Martin", "country_code": "663", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Collectivity of Saint Martin", "iso_3166_2": "MF", "iso_3166_3": "MAF", "name": "<PERSON> (French part)", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+590", "minLength": 7, "maxLength": 14}, {"capital": "Saint<PERSON><PERSON>", "citizenship": "St-Pierrais; Miquelonnais", "country_code": "666", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Territorial Collectivity of Saint Pierre and Miquelon", "iso_3166_2": "PM", "iso_3166_3": "SPM", "name": "Saint Pierre and Miquelon", "region_code": "019", "sub_region_code": "021", "eea": false, "dial_code": "+508", "currency_symbol": "€", "currency_decimals": "2", "flag": "PM.png", "minLength": 7, "maxLength": 14}, {"capital": "Kingstown", "citizenship": "<PERSON><PERSON>", "country_code": "670", "currency": "East Caribbean dollar", "currency_code": "XCD", "currency_sub_unit": "cent", "full_name": "Saint Vincent and the Grenadines", "iso_3166_2": "VC", "iso_3166_3": "VCT", "name": "Saint Vincent and the Grenadines", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "VC.png", "minLength": 7, "maxLength": 14}, {"capital": "San Marino", "citizenship": "San Marinese", "country_code": "674", "currency": "euro", "currency_code": "EUR ", "currency_sub_unit": "cent", "full_name": "Republic of San Marino", "iso_3166_2": "SM", "iso_3166_3": "SMR", "name": "San Marino", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+378", "currency_symbol": "€", "currency_decimals": "2", "flag": "SM.png", "minLength": 7, "maxLength": 14}, {"capital": "São Tomé", "citizenship": "<PERSON>", "country_code": "678", "currency": "dobra", "currency_code": "STD", "currency_sub_unit": "centavo", "full_name": "Democratic Republic of São Tomé and Príncipe", "iso_3166_2": "ST", "iso_3166_3": "STP", "name": "Sao Tome and Principe", "region_code": "002", "sub_region_code": "017", "eea": false, "dial_code": "+239", "currency_symbol": "Db", "currency_decimals": "2", "flag": "ST.png", "minLength": 7, "maxLength": 14}, {"capital": "Riyadh", "citizenship": "Saudi Arabian", "country_code": "682", "currency": "riyal", "currency_code": "SAR", "currency_sub_unit": "halala", "full_name": "Kingdom of Saudi Arabia", "iso_3166_2": "SA", "iso_3166_3": "SAU", "name": "Saudi Arabia", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+966", "currency_symbol": "﷼", "currency_decimals": "2", "flag": "SA.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Senegalese", "country_code": "686", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Republic of Senegal", "iso_3166_2": "SN", "iso_3166_3": "SEN", "name": "Senegal", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+221", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "SN.png", "minLength": 7, "maxLength": 14}, {"capital": "Belgrade", "citizenship": "Serb", "country_code": "688", "currency": "Serbian dinar", "currency_code": "RSD", "currency_sub_unit": "para (inv.)", "full_name": "Republic of Serbia", "iso_3166_2": "RS", "iso_3166_3": "SRB", "name": "Serbia", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+381", "minLength": 7, "maxLength": 14}, {"capital": "Victoria", "citizenship": "<PERSON><PERSON><PERSON><PERSON>", "country_code": "690", "currency": "Seychelles rupee", "currency_code": "SCR", "currency_sub_unit": "cent", "full_name": "Republic of Seychelles", "iso_3166_2": "SC", "iso_3166_3": "SYC", "name": "Seychelles", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+248", "currency_symbol": "₨", "currency_decimals": "2", "flag": "SC.png", "minLength": 7, "maxLength": 14}, {"capital": "Freetown", "citizenship": "Sierra Leonean", "country_code": "694", "currency": "leone", "currency_code": "SLL", "currency_sub_unit": "cent", "full_name": "Republic of Sierra Leone", "iso_3166_2": "SL", "iso_3166_3": "SLE", "name": "Sierra Leone", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+232", "currency_symbol": "Le", "currency_decimals": "2", "flag": "SL.png", "minLength": 7, "maxLength": 14}, {"capital": "Singapore", "citizenship": "Singaporean", "country_code": "702", "currency": "Singapore dollar", "currency_code": "SGD", "currency_sub_unit": "cent", "full_name": "Republic of Singapore", "iso_3166_2": "SG", "iso_3166_3": "SGP", "name": "Singapore", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+65", "currency_symbol": "$", "currency_decimals": "2", "flag": "SG.png", "minLength": 7, "maxLength": 14}, {"capital": "Bratislava", "citizenship": "Slovak", "country_code": "703", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Slovak Republic", "iso_3166_2": "SK", "iso_3166_3": "SVK", "name": "Slovakia", "region_code": "150", "sub_region_code": "151", "eea": true, "dial_code": "+421", "currency_symbol": "Sk", "currency_decimals": "2", "flag": "SK.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Vietnamese", "country_code": "704", "currency": "dong", "currency_code": "VND", "currency_sub_unit": "(10 hào", "full_name": "Socialist Republic of Vietnam", "iso_3166_2": "VN", "iso_3166_3": "VNM", "name": "VietNam", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+84", "currency_symbol": "₫", "currency_decimals": "2", "flag": "VN.png", "minLength": 7, "maxLength": 14}, {"capital": "Ljubljana", "citizenship": "Slovene", "country_code": "705", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Republic of Slovenia", "iso_3166_2": "SI", "iso_3166_3": "SVN", "name": "Slovenia", "region_code": "150", "sub_region_code": "039", "eea": true, "dial_code": "+386", "currency_symbol": "€", "currency_decimals": "2", "flag": "SI.png", "minLength": 7, "maxLength": 14}, {"capital": "Mogadishu", "citizenship": "Somali", "country_code": "706", "currency": "Somali shilling", "currency_code": "SOS", "currency_sub_unit": "cent", "full_name": "Somali Republic", "iso_3166_2": "SO", "iso_3166_3": "SOM", "name": "Somalia", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+252", "currency_symbol": "S", "currency_decimals": "2", "flag": "SO.png", "minLength": 7, "maxLength": 14}, {"capital": "Pretoria (ZA1)", "citizenship": "South African", "country_code": "710", "currency": "rand", "currency_code": "ZAR", "currency_sub_unit": "cent", "full_name": "Republic of South Africa", "iso_3166_2": "ZA", "iso_3166_3": "ZAF", "name": "South Africa", "region_code": "002", "sub_region_code": "018", "eea": false, "dial_code": "+27", "currency_symbol": "R", "currency_decimals": "2", "flag": "ZA.png", "minLength": 7, "maxLength": 14}, {"capital": "Harare", "citizenship": "Zimbabwean", "country_code": "716", "currency": "Zimbabwe dollar (ZW1)", "currency_code": "ZWL", "currency_sub_unit": "cent", "full_name": "Republic of Zimbabwe", "iso_3166_2": "ZW", "iso_3166_3": "ZWE", "name": "Zimbabwe", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+263", "currency_symbol": "Z$", "currency_decimals": "2", "flag": "ZW.png", "minLength": 7, "maxLength": 14}, {"capital": "Madrid", "citizenship": "Spaniard", "country_code": "724", "currency": "euro", "currency_code": "EUR", "currency_sub_unit": "cent", "full_name": "Kingdom of Spain", "iso_3166_2": "ES", "iso_3166_3": "ESP", "name": "Spain", "region_code": "150", "sub_region_code": "039", "eea": true, "dial_code": "+34", "currency_symbol": "€", "currency_decimals": "2", "flag": "ES.png", "minLength": 7, "maxLength": 14}, {"capital": "Juba", "citizenship": "South Sudanese", "country_code": "728", "currency": "South Sudanese pound", "currency_code": "SSP", "currency_sub_unit": "piaster", "full_name": "Republic of South Sudan", "iso_3166_2": "SS", "iso_3166_3": "SSD", "name": "South Sudan", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+211", "minLength": 7, "flag": "SD.png", "maxLength": 14}, {"capital": "Khartoum", "citizenship": "Sudanese", "country_code": "729", "currency": "Sudanese pound", "currency_code": "SDG", "currency_sub_unit": "piastre", "full_name": "Republic of the Sudan", "iso_3166_2": "SD", "iso_3166_3": "SDN", "name": "Sudan", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+249", "minLength": 7, "flag": "SD.png", "maxLength": 14}, {"capital": "Al a<PERSON>un", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "732", "currency": "Moroccan dirham", "currency_code": "MAD", "currency_sub_unit": "centime", "full_name": "Western Sahara", "iso_3166_2": "EH", "iso_3166_3": "ESH", "name": "Western Sahara", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+212", "currency_symbol": "MAD", "currency_decimals": "2", "flag": "EH.png", "minLength": 7, "maxLength": 14}, {"capital": "Paramaribo", "citizenship": "Surinamese", "country_code": "740", "currency": "Surinamese dollar", "currency_code": "SRD", "currency_sub_unit": "cent", "full_name": "Republic of Suriname", "iso_3166_2": "SR", "iso_3166_3": "SUR", "name": "Suriname", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+597", "currency_symbol": "$", "currency_decimals": "2", "flag": "SR.png", "minLength": 7, "maxLength": 14}, {"capital": "Longyearbyen", "citizenship": "of Svalbard", "country_code": "744", "currency": "Norwegian krone (pl. kroner)", "currency_code": "NOK", "currency_sub_unit": "øre (inv.)", "full_name": "Svalbard and <PERSON>", "iso_3166_2": "SJ", "iso_3166_3": "SJM", "name": "Svalbard and <PERSON>", "region_code": "150", "sub_region_code": "154", "eea": false, "dial_code": "+47", "currency_symbol": "kr", "currency_decimals": "2", "flag": "SJ.png", "minLength": 7, "maxLength": 14}, {"capital": "Mbabane", "citizenship": "Swazi", "country_code": "748", "currency": "lilangeni", "currency_code": "SZL", "currency_sub_unit": "cent", "full_name": "Kingdom of Swaziland", "iso_3166_2": "SZ", "iso_3166_3": "SWZ", "name": "Swaziland", "region_code": "002", "sub_region_code": "018", "eea": false, "dial_code": "+268", "currency_symbol": "SZL", "currency_decimals": "2", "flag": "SZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Stockholm", "citizenship": "Swedish", "country_code": "752", "currency": "krona (pl. kronor)", "currency_code": "SEK", "currency_sub_unit": "öre (inv.)", "full_name": "Kingdom of Sweden", "iso_3166_2": "SE", "iso_3166_3": "SWE", "name": "Sweden", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+46", "currency_symbol": "kr", "currency_decimals": "2", "flag": "SE.png", "minLength": 7, "maxLength": 14}, {"capital": "Bern<PERSON>", "citizenship": "Swiss", "country_code": "756", "currency": "Swiss franc", "currency_code": "CHF", "currency_sub_unit": "centime", "full_name": "Swiss Confederation", "iso_3166_2": "CH", "iso_3166_3": "CHE", "name": "Switzerland", "region_code": "150", "sub_region_code": "155", "eea": false, "dial_code": "+41", "currency_symbol": "CHF", "currency_decimals": "2", "flag": "CH.png", "minLength": 7, "maxLength": 14}, {"capital": "Damascus", "citizenship": "Syrian", "country_code": "760", "currency": "Syrian pound", "currency_code": "SYP", "currency_sub_unit": "piastre", "full_name": "Syrian Arab Republic", "iso_3166_2": "SY", "iso_3166_3": "SYR", "name": "Syrian Arab Republic", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+963", "currency_symbol": "£", "currency_decimals": "2", "flag": "SY.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>", "citizenship": "Tajik", "country_code": "762", "currency": "somoni", "currency_code": "TJS", "currency_sub_unit": "diram", "full_name": "Republic of Tajikistan", "iso_3166_2": "TJ", "iso_3166_3": "TJK", "name": "Tajikistan", "region_code": "142", "sub_region_code": "143", "eea": false, "dial_code": "+992", "currency_symbol": "TJS", "currency_decimals": "2", "flag": "TJ.png", "minLength": 7, "maxLength": 14}, {"capital": "Bangkok", "citizenship": "Thai", "country_code": "764", "currency": "baht (inv.)", "currency_code": "THB", "currency_sub_unit": "satang (inv.)", "full_name": "Kingdom of Thailand", "iso_3166_2": "TH", "iso_3166_3": "THA", "name": "Thailand", "region_code": "142", "sub_region_code": "035", "eea": false, "dial_code": "+66", "currency_symbol": "฿", "currency_decimals": "2", "flag": "TH.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Togolese", "country_code": "768", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Togolese Republic", "iso_3166_2": "TG", "iso_3166_3": "TGO", "name": "Togo", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+228", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "TG.png", "minLength": 7, "maxLength": 14}, {"capital": "(TK2)", "citizenship": "Tokelauan", "country_code": "772", "currency": "New Zealand dollar", "currency_code": "NZD", "currency_sub_unit": "cent", "full_name": "Tokelau", "iso_3166_2": "TK", "iso_3166_3": "TKL", "name": "Tokelau", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+690", "currency_symbol": "$", "currency_decimals": "2", "flag": "TK.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON><PERSON>alofa", "citizenship": "Tongan", "country_code": "776", "currency": "pa’anga (inv.)", "currency_code": "TOP", "currency_sub_unit": "seni<PERSON> (inv.)", "full_name": "Kingdom of Tonga", "iso_3166_2": "TO", "iso_3166_3": "TON", "name": "Tonga", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+676", "currency_symbol": "T$", "currency_decimals": "2", "flag": "TO.png", "minLength": 7, "maxLength": 14}, {"capital": "Port of Spain", "citizenship": "Trinidadian; Tobagonian", "country_code": "780", "currency": "Trinidad and Tobago dollar", "currency_code": "TTD", "currency_sub_unit": "cent", "full_name": "Republic of Trinidad and Tobago", "iso_3166_2": "TT", "iso_3166_3": "TTO", "name": "Trinidad and Tobago", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "TT$", "currency_decimals": "2", "flag": "TT.png", "minLength": 7, "maxLength": 14}, {"capital": "Abu Dhabi", "citizenship": "<PERSON><PERSON><PERSON>", "country_code": "784", "currency": "UAE dirham", "currency_code": "AED", "currency_sub_unit": "fils (inv.)", "full_name": "United Arab Emirates", "iso_3166_2": "AE", "iso_3166_3": "ARE", "name": "United Arab Emirates", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+971", "currency_symbol": "AED", "currency_decimals": "2", "flag": "AE.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON><PERSON>", "citizenship": "Tunisian", "country_code": "788", "currency": "Tunisian dinar", "currency_code": "TND", "currency_sub_unit": "millime", "full_name": "Republic of Tunisia", "iso_3166_2": "TN", "iso_3166_3": "TUN", "name": "Tunisia", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+216", "currency_symbol": "TND", "currency_decimals": "3", "flag": "TN.png", "minLength": 7, "maxLength": 14}, {"capital": "Ankara", "citizenship": "Turk", "country_code": "792", "currency": "Turkish lira (inv.)", "currency_code": "TRY", "currency_sub_unit": "kurus (inv.)", "full_name": "Republic of Turkey", "iso_3166_2": "TR", "iso_3166_3": "TUR", "name": "Turkey", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+90", "currency_symbol": "₺", "currency_decimals": "2", "flag": "TR.png", "minLength": 7, "maxLength": 14}, {"capital": "Ashgabat", "citizenship": "Turkmen", "country_code": "795", "currency": "Turkmen manat (inv.)", "currency_code": "TMT", "currency_sub_unit": "tenge (inv.)", "full_name": "Turkmenistan", "iso_3166_2": "TM", "iso_3166_3": "TKM", "name": "Turkmenistan", "region_code": "142", "sub_region_code": "143", "eea": false, "dial_code": "+993", "currency_symbol": "m", "currency_decimals": "2", "flag": "TM.png", "minLength": 7, "maxLength": 14}, {"capital": "Cockburn Town", "citizenship": "Turks and Caicos Islander", "country_code": "796", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "Turks and Caicos Islands", "iso_3166_2": "TC", "iso_3166_3": "TCA", "name": "Turks and Caicos Islands", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "TC.png", "minLength": 7, "maxLength": 14}, {"capital": "Funafuti", "citizenship": "Tuvaluan", "country_code": "798", "currency": "Australian dollar", "currency_code": "AUD", "currency_sub_unit": "cent", "full_name": "Tuvalu", "iso_3166_2": "TV", "iso_3166_3": "TUV", "name": "Tuvalu", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+688", "currency_symbol": "$", "currency_decimals": "2", "flag": "TV.png", "minLength": 7, "maxLength": 14}, {"capital": "Kampala", "citizenship": "Ugandan", "country_code": "800", "currency": "Uganda shilling", "currency_code": "UGX", "currency_sub_unit": "cent", "full_name": "Republic of Uganda", "iso_3166_2": "UG", "iso_3166_3": "UGA", "name": "Uganda", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+256", "currency_symbol": "UGX", "currency_decimals": "0", "flag": "UG.png", "minLength": 7, "maxLength": 14}, {"capital": "Kiev", "citizenship": "Ukrainian", "country_code": "804", "currency": "hryvnia", "currency_code": "UAH", "currency_sub_unit": "kopiyka", "full_name": "Ukraine", "iso_3166_2": "UA", "iso_3166_3": "UKR", "name": "Ukraine", "region_code": "150", "sub_region_code": "151", "eea": false, "dial_code": "+380", "currency_symbol": "₴", "currency_decimals": "2", "flag": "UA.png", "minLength": 7, "maxLength": 14}, {"capital": "Skopje", "citizenship": "of the former Yugoslav Republic of Macedonia", "country_code": "807", "currency": "denar (pl. denars)", "currency_code": "MKD", "currency_sub_unit": "deni (inv.)", "full_name": "the former Yugoslav Republic of Macedonia", "iso_3166_2": "MK", "iso_3166_3": "MKD", "name": "Macedonia, the former Yugoslav Republic of", "region_code": "150", "sub_region_code": "039", "eea": false, "dial_code": "+389", "currency_symbol": "ден", "currency_decimals": "2", "flag": "MK.png", "minLength": 7, "maxLength": 14}, {"capital": "Cairo", "citizenship": "Egyptian", "country_code": "818", "currency": "Egyptian pound", "currency_code": "EGP", "currency_sub_unit": "piastre", "full_name": "Arab Republic of Egypt", "iso_3166_2": "EG", "iso_3166_3": "EGY", "name": "Egypt", "region_code": "002", "sub_region_code": "015", "eea": false, "dial_code": "+20", "currency_symbol": "£", "currency_decimals": "2", "flag": "EG.png", "minLength": 7, "maxLength": 14}, {"capital": "London", "citizenship": "British", "country_code": "826", "currency": "pound sterling", "currency_code": "GBP", "currency_sub_unit": "penny (pl. pence)", "full_name": "United Kingdom of Great Britain and Northern Ireland", "iso_3166_2": "GB", "iso_3166_3": "GBR", "name": "United Kingdom", "region_code": "150", "sub_region_code": "154", "eea": true, "dial_code": "+44", "currency_symbol": "£", "currency_decimals": "2", "flag": "GB.png", "minLength": 7, "maxLength": 14}, {"capital": "St Peter Port", "citizenship": "of Guernsey", "country_code": "831", "currency": "Guernsey pound (GG2)", "currency_code": "GGP (GG2)", "currency_sub_unit": "penny (pl. pence)", "full_name": "Bailiwick of Guernsey", "iso_3166_2": "GG", "iso_3166_3": "GGY", "name": "Guernsey", "region_code": "150", "sub_region_code": "154", "eea": false, "dial_code": "+44", "minLength": 7, "maxLength": 14}, {"capital": "St Helier", "citizenship": "of Jersey", "country_code": "832", "currency": "Jersey pound (JE2)", "currency_code": "JEP (JE2)", "currency_sub_unit": "penny (pl. pence)", "full_name": "Bailiwick of Jersey", "iso_3166_2": "JE", "iso_3166_3": "JEY", "name": "Jersey", "region_code": "150", "sub_region_code": "154", "eea": false, "dial_code": "+44", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON>", "citizenship": "<PERSON><PERSON><PERSON>; Manxwoman", "country_code": "833", "currency": "Manx pound (IM2)", "currency_code": "IMP (IM2)", "currency_sub_unit": "penny (pl. pence)", "full_name": "Isle of Man", "iso_3166_2": "IM", "iso_3166_3": "IMN", "name": "Isle of Man", "region_code": "150", "sub_region_code": "154", "eea": false, "dial_code": "+44", "minLength": 7, "maxLength": 14}, {"capital": "Dodoma (TZ1)", "citizenship": "Tanzanian", "country_code": "834", "currency": "Tanzanian shilling", "currency_code": "TZS", "currency_sub_unit": "cent", "full_name": "United Republic of Tanzania", "iso_3166_2": "TZ", "iso_3166_3": "TZA", "name": "Tanzania, United Republic of", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+255", "currency_symbol": "TZS", "currency_decimals": "2", "flag": "TZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Washington DC", "citizenship": "American", "country_code": "840", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "United States of America", "iso_3166_2": "US", "iso_3166_3": "USA", "name": "United States", "region_code": "019", "sub_region_code": "021", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "US.png", "minLength": 7, "maxLength": 14}, {"capital": "<PERSON>", "citizenship": "US Virgin Islander", "country_code": "850", "currency": "US dollar", "currency_code": "USD", "currency_sub_unit": "cent", "full_name": "United States Virgin Islands", "iso_3166_2": "VI", "iso_3166_3": "VIR", "name": "Virgin Islands, U.S.", "region_code": "019", "sub_region_code": "029", "eea": false, "dial_code": "+1", "currency_symbol": "$", "currency_decimals": "2", "flag": "VI.png", "minLength": 7, "maxLength": 14}, {"capital": "Ouagadougou", "citizenship": "Burkinabe", "country_code": "854", "currency": "CFA franc (BCEAO)", "currency_code": "XOF", "currency_sub_unit": "centime", "full_name": "Burkina Faso", "iso_3166_2": "BF", "iso_3166_3": "BFA", "name": "Burkina Faso", "region_code": "002", "sub_region_code": "011", "eea": false, "dial_code": "+226", "currency_symbol": "XOF", "currency_decimals": "0", "flag": "BF.png", "minLength": 7, "maxLength": 14}, {"capital": "Montevideo", "citizenship": "Uruguayan", "country_code": "858", "currency": "Uruguayan peso", "currency_code": "UYU", "currency_sub_unit": "centésimo", "full_name": "Eastern Republic of Uruguay", "iso_3166_2": "UY", "iso_3166_3": "URY", "name": "Uruguay", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+598", "currency_symbol": "$U", "currency_decimals": "0", "flag": "UY.png", "minLength": 7, "maxLength": 14}, {"capital": "Tashkent", "citizenship": "Uzbek", "country_code": "860", "currency": "sum (inv.)", "currency_code": "UZS", "currency_sub_unit": "tiyin (inv.)", "full_name": "Republic of Uzbekistan", "iso_3166_2": "UZ", "iso_3166_3": "UZB", "name": "Uzbekistan", "region_code": "142", "sub_region_code": "143", "eea": false, "dial_code": "+998", "currency_symbol": "лв", "currency_decimals": "2", "flag": "UZ.png", "minLength": 7, "maxLength": 14}, {"capital": "Caracas", "citizenship": "Venezuelan", "country_code": "862", "currency": "bol<PERSON>var fuerte (pl. bol<PERSON><PERSON><PERSON> fuertes)", "currency_code": "VEF", "currency_sub_unit": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "Bolivarian Republic of Venezuela", "iso_3166_2": "VE", "iso_3166_3": "VEN", "name": "Venezuela, Bolivarian Republic of", "region_code": "019", "sub_region_code": "005", "eea": false, "dial_code": "+58", "currency_symbol": "Bs", "currency_decimals": "2", "flag": "VE.png", "minLength": 7, "maxLength": 14}, {"capital": "Mata-Utu", "citizenship": "Wallisian; Futunan; Wallis and Futuna Islander", "country_code": "876", "currency": "CFP franc", "currency_code": "XPF", "currency_sub_unit": "centime", "full_name": "Wallis and Futuna", "iso_3166_2": "WF", "iso_3166_3": "WLF", "name": "Wallis and Futuna", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+681", "currency_symbol": "XPF", "currency_decimals": "0", "flag": "WF.png", "minLength": 7, "maxLength": 14}, {"capital": "Apia", "citizenship": "Samoan", "country_code": "882", "currency": "tala (inv.)", "currency_code": "WST", "currency_sub_unit": "sene (inv.)", "full_name": "Independent State of Samoa", "iso_3166_2": "WS", "iso_3166_3": "WSM", "name": "Samoa", "region_code": "009", "sub_region_code": "061", "eea": false, "dial_code": "+685", "currency_symbol": "WS$", "currency_decimals": "2", "flag": "WS.png", "minLength": 7, "maxLength": 14}, {"capital": "San’a", "citizenship": "Yemenite", "country_code": "887", "currency": "Yemeni rial", "currency_code": "YER", "currency_sub_unit": "fils (inv.)", "full_name": "Republic of Yemen", "iso_3166_2": "YE", "iso_3166_3": "YEM", "name": "Yemen", "region_code": "142", "sub_region_code": "145", "eea": false, "dial_code": "+967", "currency_symbol": "﷼", "currency_decimals": "2", "flag": "YE.png", "minLength": 7, "maxLength": 14}, {"capital": "Lusaka", "citizenship": "Zambian", "country_code": "894", "currency": "Zambian kwacha (inv.)", "currency_code": "ZMW", "currency_sub_unit": "ngwee (inv.)", "full_name": "Republic of Zambia", "iso_3166_2": "ZM", "iso_3166_3": "ZMB", "name": "Zambia", "region_code": "002", "sub_region_code": "014", "eea": false, "dial_code": "+260", "currency_symbol": "ZK", "currency_decimals": "2", "flag": "ZM.png", "minLength": 7, "maxLength": 14}]