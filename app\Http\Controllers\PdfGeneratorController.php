<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf as PDF;

class PdfGeneratorController extends Controller
{
    public function index()
    {
        $data = [
            'imagePath'    => public_path('img/profile.png'),
            'name'         => '<PERSON>',
            'address'      => 'USA',
            'mobileNumber' => '000000000',
            'email'        => '<EMAIL>'
        ];
        $pdf = PDF::loadView('resume', $data);
        return $pdf->stream('resume.pdf');
    }
}
