<?php

/**
 * Bidding System Migration Script
 * 
 * This script helps automate the migration of bidding system files
 * from the source project to a target Laravel project.
 * 
 * Usage: php bidding-migration-script.php
 */

class BiddingMigrationScript
{
    private $sourceDir;
    private $targetDir;
    private $logFile;

    public function __construct($sourceDir, $targetDir)
    {
        $this->sourceDir = rtrim($sourceDir, '/');
        $this->targetDir = rtrim($targetDir, '/');
        $this->logFile = $this->targetDir . '/bidding-migration.log';
    }

    /**
     * Main migration method
     */
    public function migrate()
    {
        $this->log("Starting bidding system migration...");
        
        try {
            $this->validateDirectories();
            $this->createBackup();
            $this->copyFiles();
            $this->updateComposerJson();
            $this->updateEnvironmentFile();
            $this->updateModels();
            $this->updateRoutes();
            $this->generateMigrationCommands();
            
            $this->log("Migration completed successfully!");
            echo "Migration completed! Check {$this->logFile} for details.\n";
            
        } catch (Exception $e) {
            $this->log("Migration failed: " . $e->getMessage());
            echo "Migration failed: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Validate source and target directories
     */
    private function validateDirectories()
    {
        if (!is_dir($this->sourceDir)) {
            throw new Exception("Source directory does not exist: {$this->sourceDir}");
        }
        
        if (!is_dir($this->targetDir)) {
            throw new Exception("Target directory does not exist: {$this->targetDir}");
        }
        
        if (!file_exists($this->targetDir . '/artisan')) {
            throw new Exception("Target directory is not a Laravel project: {$this->targetDir}");
        }
        
        $this->log("Directories validated successfully");
    }

    /**
     * Create backup of target project
     */
    private function createBackup()
    {
        $backupDir = $this->targetDir . '/backup-' . date('Y-m-d-H-i-s');
        
        // Create backup of critical files
        $criticalFiles = [
            'app/Models/User.php',
            'app/Models/Request/Request.php',
            'routes/api/v1/request.php',
            'composer.json',
            '.env'
        ];
        
        foreach ($criticalFiles as $file) {
            $sourcePath = $this->targetDir . '/' . $file;
            if (file_exists($sourcePath)) {
                $backupPath = $backupDir . '/' . $file;
                $this->ensureDirectoryExists(dirname($backupPath));
                copy($sourcePath, $backupPath);
            }
        }
        
        $this->log("Backup created at: $backupDir");
    }

    /**
     * Copy bidding system files
     */
    private function copyFiles()
    {
        $filesToCopy = [
            // Database migrations
            'database/migrations/2023_05_15_095252_update_requests_for_offerred_ride_fare_update_users_for_bidd_ride_table.php',
            
            // Controllers
            'app/Http/Controllers/Api/V1/Request/CreateNewRequestController.php',
            'app/Http/Controllers/Api/V1/Request/CreateRequestController.php',
            'app/Http/Controllers/Web/DetailEmailController.php',
            
            // Helpers
            'app/Helpers/Rides/RidePriceCalculationHelpers.php',
            'app/Helpers/Rides/CalculatAdminCommissionAndTaxHelper.php',
            'app/Helpers/Rides/FetchDriversFromFirebaseHelpers.php',
            
            // Transformers
            'app/Transformers/Requests/TripRequestTransformer.php',
            'app/Transformers/Driver/DriverProfileTransformer.php',
            
            // Jobs
            'app/Jobs/NoDriverFoundNotifyJob.php',
            'app/Jobs/SendRequestToNextDriversJob.php',
            
            // Configuration
            'config/firebase.php',
            'public/push-configurations/bidding_firebase.json',
            
            // Constants
            'app/Base/Constants/Setting/Settings.php',
        ];

        foreach ($filesToCopy as $file) {
            $this->copyFile($file);
        }
    }

    /**
     * Copy individual file
     */
    private function copyFile($relativePath)
    {
        $sourcePath = $this->sourceDir . '/' . $relativePath;
        $targetPath = $this->targetDir . '/' . $relativePath;
        
        if (!file_exists($sourcePath)) {
            $this->log("Warning: Source file not found: $sourcePath");
            return;
        }
        
        $this->ensureDirectoryExists(dirname($targetPath));
        
        if (file_exists($targetPath)) {
            $this->log("File already exists, creating backup: $relativePath");
            copy($targetPath, $targetPath . '.backup');
        }
        
        copy($sourcePath, $targetPath);
        $this->log("Copied: $relativePath");
    }

    /**
     * Update composer.json with required packages
     */
    private function updateComposerJson()
    {
        $composerPath = $this->targetDir . '/composer.json';
        
        if (!file_exists($composerPath)) {
            throw new Exception("composer.json not found in target directory");
        }
        
        $composer = json_decode(file_get_contents($composerPath), true);
        
        // Add required packages
        $requiredPackages = [
            'saikiran/geohash' => '^1.0',
            'kreait/firebase-php' => '^6.0',
            'spatie/laravel-fractal' => '^6.0',
            'ramsey/uuid' => '^4.5'
        ];
        
        foreach ($requiredPackages as $package => $version) {
            if (!isset($composer['require'][$package])) {
                $composer['require'][$package] = $version;
                $this->log("Added package: $package");
            }
        }
        
        // Add helpers to autoload
        if (!isset($composer['autoload']['files'])) {
            $composer['autoload']['files'] = [];
        }
        
        if (!in_array('app/Helpers/helpers.php', $composer['autoload']['files'])) {
            $composer['autoload']['files'][] = 'app/Helpers/helpers.php';
            $this->log("Added helpers.php to autoload");
        }
        
        file_put_contents($composerPath, json_encode($composer, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->log("Updated composer.json");
    }

    /**
     * Update .env file with Firebase configuration
     */
    private function updateEnvironmentFile()
    {
        $envPath = $this->targetDir . '/.env';
        
        if (!file_exists($envPath)) {
            $this->log("Warning: .env file not found");
            return;
        }
        
        $envContent = file_get_contents($envPath);
        
        $firebaseConfig = "\n# Firebase Configuration\n";
        $firebaseConfig .= "FIREBASE_CREDENTIALS=/path/to/your/firebase-credentials.json\n";
        $firebaseConfig .= "FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com\n";
        $firebaseConfig .= "\n# Bidding Configuration\n";
        $firebaseConfig .= "APP_FOR=bidding\n";
        
        if (strpos($envContent, 'FIREBASE_CREDENTIALS') === false) {
            file_put_contents($envPath, $envContent . $firebaseConfig);
            $this->log("Added Firebase configuration to .env");
        }
    }

    /**
     * Update models with bidding fields
     */
    private function updateModels()
    {
        // Update User model
        $this->updateUserModel();
        
        // Update Request model
        $this->updateRequestModel();
    }

    /**
     * Update User model
     */
    private function updateUserModel()
    {
        $userModelPath = $this->targetDir . '/app/Models/User.php';
        
        if (!file_exists($userModelPath)) {
            $this->log("Warning: User model not found");
            return;
        }
        
        $content = file_get_contents($userModelPath);
        
        // Add is_bid_app to fillable if not present
        if (strpos($content, 'is_bid_app') === false) {
            $content = str_replace(
                "protected \$fillable = [",
                "protected \$fillable = [\n        'is_bid_app',",
                $content
            );
            
            file_put_contents($userModelPath, $content);
            $this->log("Updated User model with bidding fields");
        }
    }

    /**
     * Update Request model
     */
    private function updateRequestModel()
    {
        $requestModelPath = $this->targetDir . '/app/Models/Request/Request.php';
        
        if (!file_exists($requestModelPath)) {
            $this->log("Warning: Request model not found");
            return;
        }
        
        $content = file_get_contents($requestModelPath);
        
        // Add bidding fields to fillable if not present
        $biddingFields = ['offerred_ride_fare', 'accepted_ride_fare', 'is_bid_ride'];
        
        foreach ($biddingFields as $field) {
            if (strpos($content, $field) === false) {
                $content = str_replace(
                    "protected \$fillable = [",
                    "protected \$fillable = [\n        '$field',",
                    $content
                );
            }
        }
        
        file_put_contents($requestModelPath, $content);
        $this->log("Updated Request model with bidding fields");
    }

    /**
     * Update routes with bidding endpoints
     */
    private function updateRoutes()
    {
        $routesPath = $this->targetDir . '/routes/api/v1/request.php';
        
        if (!file_exists($routesPath)) {
            $this->log("Warning: Request routes file not found");
            return;
        }
        
        $content = file_get_contents($routesPath);
        
        // Add bidding route if not present
        if (strpos($content, 'respond-for-bid') === false) {
            $biddingRoute = "\n        // Accept/Decline Bid Request\n";
            $biddingRoute .= "        Route::post('respond-for-bid', 'CreateNewRequestController@respondForBid');\n";
            
            // Find a good place to insert the route
            $content = str_replace(
                "Route::post('cancel', 'UserCancelRequestController@cancelRequest');",
                "Route::post('cancel', 'UserCancelRequestController@cancelRequest');" . $biddingRoute,
                $content
            );
            
            file_put_contents($routesPath, $content);
            $this->log("Added bidding routes");
        }
    }

    /**
     * Generate migration commands
     */
    private function generateMigrationCommands()
    {
        $commands = [
            'composer install',
            'php artisan migrate',
            'php artisan db:seed --class=SettingsSeeder',
            'php artisan config:clear',
            'php artisan cache:clear',
            'php artisan route:clear'
        ];
        
        $commandsFile = $this->targetDir . '/bidding-migration-commands.sh';
        file_put_contents($commandsFile, "#!/bin/bash\n\n" . implode("\n", $commands) . "\n");
        chmod($commandsFile, 0755);
        
        $this->log("Generated migration commands file: bidding-migration-commands.sh");
    }

    /**
     * Ensure directory exists
     */
    private function ensureDirectoryExists($dir)
    {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }

    /**
     * Log message
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
        echo $logMessage;
    }
}

// Usage
if ($argc < 3) {
    echo "Usage: php bidding-migration-script.php <source_directory> <target_directory>\n";
    echo "Example: php bidding-migration-script.php /path/to/source/project /path/to/target/project\n";
    exit(1);
}

$sourceDir = $argv[1];
$targetDir = $argv[2];

$migrator = new BiddingMigrationScript($sourceDir, $targetDir);
$migrator->migrate();
